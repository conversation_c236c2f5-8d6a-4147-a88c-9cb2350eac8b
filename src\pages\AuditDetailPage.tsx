import { useState, useEffect, useRef } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { api } from "@/services/api";
import { VideoMaterial, AuditStatus } from "@/types";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "@/components/ui/use-toast";
import { AppLayout } from "@/components/layout/AppLayout";
import { ArrowLeft, CheckCircle, XCircle, Clock } from "lucide-react";
import { Label } from "@/components/ui/label";

// Video Player Component (Read-only)
const VideoPlayer = ({ src }: { src: string }) => {
  const videoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateVideoHeight = () => {
      if (videoRef.current) {
        const height = videoRef.current.offsetHeight;
        document.documentElement.style.setProperty('--video-height', `${height}px`);
      }
    };

    updateVideoHeight();
    window.addEventListener('resize', updateVideoHeight);
    return () => window.removeEventListener('resize', updateVideoHeight);
  }, []);

  return (
    <div ref={videoRef} className="relative aspect-video w-full overflow-hidden rounded-md">
      <video
        controls
        className="h-full w-full"
        poster="https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGNvZGV8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60"
      >
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

// Transcript Component (Read-only)
const Transcript = ({
  transcript
}: {
  transcript: { startTime: number, endTime: number, text: string }[] | { time: string, text: string }[]
}) => (
  <div className="space-y-2">
    {transcript.map((item, index) => (
      <div key={index} className="flex text-sm hover:bg-gray-800 p-2 rounded-md transition-colors">
        <span className="w-20 flex-shrink-0 text-gray-400">
          {'startTime' in item
            ? new Date(item.startTime * 1000).toISOString().substring(14, 5)
            : 'time' in item ? item.time : ''
          }
        </span>
        <span>{item.text}</span>
      </div>
    ))}
  </div>
);

// TranscriptPanel Component (Read-only)
const TranscriptPanel = ({
  transcript
}: {
  transcript?: { startTime: number, endTime: number, text: string }[] | { time: string, text: string }[]
}) => (
  <div className="relative w-full h-[var(--video-height,0px)] overflow-hidden rounded-md bg-gray-900">
    {transcript ? (
      <div className="absolute inset-0 overflow-y-auto p-4 scrollbar-thin">
        <Transcript transcript={transcript} />
      </div>
    ) : (
      <div className="absolute inset-0 flex items-center justify-center text-gray-400">
        暂无配音文稿
      </div>
    )}
  </div>
);

// Feature List Component (Read-only)
const FeatureList = ({ features }: { features: VideoMaterial["features"] }) => (
  <div className="space-y-3">
    {features?.map((feature) => (
      <div key={feature.id || Math.random().toString()} className="flex items-start p-2 hover:bg-gray-800 rounded-md transition-colors">
        {feature.imageUrl ? (
          <img
            src={feature.imageUrl}
            alt={feature.name}
            className="h-12 w-12 object-cover rounded-md mr-3"
          />
        ) : (
          <div className="h-12 w-12 bg-gray-700 rounded-md mr-3 flex items-center justify-center">
            <span className="text-xs text-gray-400">No Image</span>
          </div>
        )}
        <div className="flex-1">
          <div className="flex justify-between items-start">
            <div>
              <Badge variant={feature.type === "敏感人脸" ? "destructive" : feature.type === "敏感标志物" ? "outline" : "secondary"}>
                {feature.type}
              </Badge>
              <h4 className="font-medium mt-1">{feature.name}</h4>
            </div>
            <div className="text-xs text-gray-400 flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              <span>
                {'timestamp' in feature
                  ? new Date(feature.timestamp * 1000).toISOString().substring(14, 5)
                  : 'time' in feature ? feature.time : ''
                }
              </span>
            </div>
          </div>
          {'confidence' in feature && typeof feature.confidence === 'number' && (
            <div className="mt-1 text-sm text-gray-400">
              置信度: {Math.round(feature.confidence * 100)}%
            </div>
          )}
        </div>
      </div>
    ))}
  </div>
);

// FeaturesPanel Component (Read-only)
const FeaturesPanel = ({
  features
}: {
  features?: VideoMaterial["features"]
}) => (
  <div className="relative w-full h-[var(--video-height,0px)] overflow-hidden rounded-md bg-gray-900">
    {features && features.length > 0 ? (
      <div className="absolute inset-0 overflow-y-auto p-4 scrollbar-thin">
        <FeatureList features={features} />
      </div>
    ) : (
      <div className="absolute inset-0 flex items-center justify-center text-gray-400">
        暂无特征信息
      </div>
    )}
  </div>
);

// Read-only Catalog Display Component
const CatalogDisplay = ({ catalogInfo }: { catalogInfo?: Record<string, string> }) => {
  const defaultCatalogInfo = catalogInfo || {
    "标题": "",
    "类别": "",
    "关键词": "",
    "简介": "",
    "地点": "",
    "时间": ""
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(defaultCatalogInfo).map(([key, value]) => (
          <div key={key} className="space-y-1">
            <Label className="text-gray-400">{key}</Label>
            <div className="bg-gray-800 p-2 rounded-md min-h-[40px] flex items-center">
              {value || <span className="text-gray-500">暂无数据</span>}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Audit Actions Component
const AuditActions = ({ 
  materialId, 
  onAuditComplete 
}: { 
  materialId: string;
  onAuditComplete: () => void;
}) => {
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [rejectReason, setRejectReason] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const handleApprove = async () => {
    setIsProcessing(true);
    try {
      await api.auditMaterial(materialId, AuditStatus.APPROVED);
      toast({
        title: "审核通过",
        description: "素材已通过审核，已移至发布库",
      });
      onAuditComplete();
    } catch (error) {
      console.error("Failed to approve material", error);
      toast({
        title: "错误",
        description: "审核操作失败",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      toast({
        title: "错误",
        description: "请填写审核不通过的理由",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    try {
      await api.auditMaterial(materialId, AuditStatus.REJECTED, rejectReason);
      toast({
        title: "审核不通过",
        description: "素材已退回至素材库",
      });
      setShowRejectDialog(false);
      setRejectReason("");
      onAuditComplete();
    } catch (error) {
      console.error("Failed to reject material", error);
      toast({
        title: "错误",
        description: "审核操作失败",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <>
      <div className="flex justify-center space-x-4 p-6 bg-gray-800/50 rounded-lg">
        <Button
          size="lg"
          className="bg-green-600 hover:bg-green-700 text-white px-8"
          onClick={handleApprove}
          disabled={isProcessing}
        >
          <CheckCircle className="h-5 w-5 mr-2" />
          审核通过
        </Button>
        <Button
          size="lg"
          variant="destructive"
          className="px-8"
          onClick={() => setShowRejectDialog(true)}
          disabled={isProcessing}
        >
          <XCircle className="h-5 w-5 mr-2" />
          审核不通过
        </Button>
      </div>

      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>审核不通过</DialogTitle>
            <DialogDescription>
              请填写审核不通过的理由，素材将退回至素材库。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              placeholder="请详细说明审核不通过的原因..."
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowRejectDialog(false);
                setRejectReason("");
              }}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={isProcessing || !rejectReason.trim()}
            >
              {isProcessing ? "处理中..." : "确认不通过"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

const AuditDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [material, setMaterial] = useState<VideoMaterial | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Handle keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedImage || !material?.thumbnails) return;

      if (e.key === 'Escape') {
        setSelectedImage(null);
        return;
      }

      const currentIndex = material.thumbnails.indexOf(selectedImage);

      if (e.key === 'ArrowLeft' && currentIndex > 0) {
        setSelectedImage(material.thumbnails[currentIndex - 1]);
      }

      if (e.key === 'ArrowRight' && currentIndex < material.thumbnails.length - 1) {
        setSelectedImage(material.thumbnails[currentIndex + 1]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedImage, material]);

  useEffect(() => {
    if (!id) return;

    const loadMaterial = async () => {
      setIsLoading(true);
      try {
        const data = await api.getMaterialById(id);
        if (data) {
          setMaterial(data);
        }
      } catch (error) {
        console.error("Failed to load material details", error);
        toast({
          title: "错误",
          description: "加载素材详情失败",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadMaterial();
  }, [id]);

  const handleAuditComplete = () => {
    navigate("/audits");
  };

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-xl">加载中...</div>
        </div>
      </AppLayout>
    );
  }

  if (!material) {
    return (
      <AppLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <div className="text-xl mb-4">素材不存在</div>
          <Button onClick={() => navigate("/audits")}>返回审核库</Button>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      {/* Lightbox for full-size image viewing */}
      {selectedImage && material?.thumbnails && (
        <div
          className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-[90vh] w-full">
            <img
              src={selectedImage}
              alt="Full size"
              className="w-full h-full object-contain"
            />

            {/* Close button */}
            <Button
              variant="outline"
              size="icon"
              className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white border-none"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedImage(null);
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </Button>

            {/* Navigation buttons */}
            <div className="absolute inset-y-0 left-0 right-0 flex justify-between items-center px-4">
              {/* Previous button */}
              {material.thumbnails.indexOf(selectedImage) > 0 && (
                <Button
                  variant="outline"
                  size="icon"
                  className="bg-black/50 hover:bg-black/70 text-white border-none h-10 w-10"
                  onClick={(e) => {
                    e.stopPropagation();
                    const currentIndex = material.thumbnails!.indexOf(selectedImage);
                    if (currentIndex > 0) {
                      setSelectedImage(material.thumbnails![currentIndex - 1]);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                    <polyline points="15 18 9 12 15 6"></polyline>
                  </svg>
                </Button>
              )}

              {/* Next button */}
              {material.thumbnails.indexOf(selectedImage) < material.thumbnails.length - 1 && (
                <Button
                  variant="outline"
                  size="icon"
                  className="bg-black/50 hover:bg-black/70 text-white border-none h-10 w-10"
                  onClick={(e) => {
                    e.stopPropagation();
                    const currentIndex = material.thumbnails!.indexOf(selectedImage);
                    if (currentIndex < material.thumbnails!.length - 1) {
                      setSelectedImage(material.thumbnails![currentIndex + 1]);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </Button>
              )}
            </div>

            {/* Image counter */}
            <div className="absolute bottom-2 left-0 right-0 text-center text-white text-sm">
              {material.thumbnails.indexOf(selectedImage) + 1} / {material.thumbnails.length}
            </div>
          </div>
        </div>
      )}

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" onClick={() => navigate("/audits")}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">{material.name}</h1>
            <Badge 
              variant={material.auditStatus === AuditStatus.PENDING ? "secondary" : "outline"}
              className="ml-2"
            >
              {material.auditStatus === AuditStatus.PENDING ? "待审核" : material.auditStatus}
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Video Player and Thumbnails */}
          <div className="lg:col-span-2 space-y-4">
            <Card className="glass-morphism overflow-hidden">
              <CardContent className="p-0">
                <Tabs defaultValue="preview">
                  <TabsList className="w-full grid grid-cols-2">
                    <TabsTrigger value="preview">视频预览</TabsTrigger>
                    <TabsTrigger value="photowall">照片墙</TabsTrigger>
                  </TabsList>
                  <div className="p-4">
                    <TabsContent value="preview" className="mt-0">
                      <VideoPlayer src="https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4" />
                    </TabsContent>
                    <TabsContent value="photowall" className="mt-0">
                      {material.thumbnails && (
                        <div className="relative aspect-video w-full overflow-hidden rounded-md bg-gray-900 flex items-center justify-center">
                          <div className="w-full h-full overflow-y-auto p-4">
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                              {material.thumbnails.map((thumbnail, index) => (
                                <div
                                  key={index}
                                  className="relative group cursor-pointer"
                                  onClick={() => setSelectedImage(thumbnail)}
                                >
                                  <img
                                    src={thumbnail}
                                    alt={`缩略图 ${index + 1}`}
                                    className="w-full aspect-video object-cover rounded-md transition-transform duration-300 group-hover:scale-105"
                                  />
                                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center rounded-md">
                                    <span className="text-white text-sm font-medium">点击查看</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>

            <Card className="glass-morphism">
              <CardContent className="p-0">
                <Tabs defaultValue="summary">
                  <TabsList className="w-full grid grid-cols-2">
                    <TabsTrigger value="summary">内容摘要</TabsTrigger>
                    <TabsTrigger value="info">视频基本信息</TabsTrigger>
                  </TabsList>
                  <div className="p-4">
                    <TabsContent value="summary" className="mt-0">
                      <div className="space-y-4">
                        <div className="bg-gray-800 p-4 rounded-md min-h-[100px]">
                          {material.summary || <span className="text-gray-500">暂无内容摘要</span>}
                        </div>
                      </div>
                    </TabsContent>
                    <TabsContent value="info" className="mt-0">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {material.duration && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">时长</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{typeof material.duration === 'number' ? `${Math.floor(material.duration / 60)}:${(material.duration % 60).toString().padStart(2, '0')}` : material.duration}</div>
                          </div>
                        )}
                        {material.resolution && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">分辨率</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.resolution}</div>
                          </div>
                        )}
                        {material.bitrate && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">码率</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.bitrate}</div>
                          </div>
                        )}
                        {material.codec && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">编码格式</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.codec}</div>
                          </div>
                        )}
                        {material.format && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">文件格式</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.format}</div>
                          </div>
                        )}
                        {material.fileSize && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">文件大小</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.fileSize}</div>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>

            <Card className="glass-morphism">
              <CardContent className="p-0">
                <Tabs defaultValue="catalog">
                  <TabsList className="w-full grid grid-cols-1">
                    <TabsTrigger value="catalog">人工编目</TabsTrigger>
                  </TabsList>
                  <div className="p-4">
                    <TabsContent value="catalog" className="mt-0">
                      <CatalogDisplay catalogInfo={material.catalogInfo} />
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>

            {/* Audit Actions */}
            {material.auditStatus === AuditStatus.PENDING && (
              <Card className="glass-morphism">
                <CardContent className="p-4">
                  <AuditActions 
                    materialId={material.id} 
                    onAuditComplete={handleAuditComplete}
                  />
                </CardContent>
              </Card>
            )}
          </div>

          {/* Video Information and Tabs */}
          <div className="space-y-6">
            <Card className="glass-morphism overflow-hidden">
              <CardContent className="p-0">
                <Tabs defaultValue="transcript">
                  <TabsList className="w-full grid grid-cols-2">
                    <TabsTrigger value="transcript">配音文稿</TabsTrigger>
                    <TabsTrigger value="features">特征信息</TabsTrigger>
                  </TabsList>
                  <div className="p-4">
                    <TabsContent value="transcript" className="mt-0">
                      <TranscriptPanel transcript={material.transcript} />
                    </TabsContent>
                    <TabsContent value="features" className="mt-0">
                      <FeaturesPanel features={material.features} />
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default AuditDetailPage;
