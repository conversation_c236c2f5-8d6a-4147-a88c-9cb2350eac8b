
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Eye, Edit, Trash } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

// Mock data structure for person libraries
interface PersonLibrary {
  id: string;
  name: string;
  description: string;
  peopleCount: number;
  createdAt: string;
}

// Mock data
const mockPersonLibraries: PersonLibrary[] = [
  {
    id: "pl-1",
    name: "政府官员",
    description: "包含各级政府官员的人脸库",
    peopleCount: 155,
    createdAt: "2023-01-15",
  },
  {
    id: "pl-2",
    name: "公众人物",
    description: "包含演员、歌手等知名公众人物的人脸库",
    peopleCount: 237,
    createdAt: "2023-02-10",
  },
  {
    id: "pl-3",
    name: "新闻主播",
    description: "各大电视台新闻主播人脸库",
    peopleCount: 43,
    createdAt: "2023-03-22",
  },
];

const PeoplePage = () => {
  const [libraries, setLibraries] = useState<PersonLibrary[]>(mockPersonLibraries);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [newLibraryName, setNewLibraryName] = useState("");
  const [newLibraryDescription, setNewLibraryDescription] = useState("");
  const [selectedLibrary, setSelectedLibrary] = useState<PersonLibrary | null>(null);

  // Filter libraries based on search query
  const filteredLibraries = libraries.filter(library =>
    library.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    library.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddLibrary = () => {
    if (!newLibraryName.trim()) {
      toast({
        title: "错误",
        description: "人物库名称不能为空",
        variant: "destructive",
      });
      return;
    }

    const newLibrary: PersonLibrary = {
      id: `pl-${Date.now()}`,
      name: newLibraryName,
      description: newLibraryDescription,
      peopleCount: 0,
      createdAt: new Date().toISOString().split('T')[0],
    };

    setLibraries([...libraries, newLibrary]);
    setNewLibraryName("");
    setNewLibraryDescription("");
    setIsAddDialogOpen(false);
    
    toast({
      title: "创建成功",
      description: `人物库 "${newLibraryName}" 已成功创建`,
    });
  };

  const handleEditLibrary = () => {
    if (!selectedLibrary) return;
    
    if (!newLibraryName.trim()) {
      toast({
        title: "错误",
        description: "人物库名称不能为空",
        variant: "destructive",
      });
      return;
    }

    const updatedLibraries = libraries.map(lib => 
      lib.id === selectedLibrary.id 
        ? { ...lib, name: newLibraryName, description: newLibraryDescription } 
        : lib
    );

    setLibraries(updatedLibraries);
    setIsEditDialogOpen(false);
    
    toast({
      title: "修改成功",
      description: `人物库 "${newLibraryName}" 已更新`,
    });
  };

  const handleDeleteLibrary = () => {
    if (!selectedLibrary) return;

    const updatedLibraries = libraries.filter(lib => lib.id !== selectedLibrary.id);
    setLibraries(updatedLibraries);
    setIsDeleteDialogOpen(false);
    
    toast({
      title: "删除成功",
      description: `人物库 "${selectedLibrary.name}" 已删除`,
      variant: "destructive",
    });
  };

  const openEditDialog = (library: PersonLibrary) => {
    setSelectedLibrary(library);
    setNewLibraryName(library.name);
    setNewLibraryDescription(library.description);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (library: PersonLibrary) => {
    setSelectedLibrary(library);
    setIsDeleteDialogOpen(true);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">人物库</h1>
          <Button 
            className="flex items-center gap-2"
            onClick={() => {
              setNewLibraryName("");
              setNewLibraryDescription("");
              setIsAddDialogOpen(true);
            }}
          >
            <PlusCircle className="w-4 h-4" />
            新建人物库
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>人物库列表</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex w-full max-w-sm">
                <Input
                  placeholder="搜索人物库..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredLibraries.length > 0 ? (
                  filteredLibraries.map((library) => (
                    <Card key={library.id} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-lg">{library.name}</CardTitle>
                          <div className="flex gap-1">
                            <Button variant="ghost" size="icon" onClick={() => openEditDialog(library)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" onClick={() => openDeleteDialog(library)}>
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground text-sm line-clamp-2 mb-2">{library.description}</p>
                        <div className="flex justify-between items-center text-sm">
                          <span>包含 {library.peopleCount} 人</span>
                          <span className="text-muted-foreground">创建于 {library.createdAt}</span>
                        </div>
                        <Button variant="outline" className="w-full mt-4" onClick={() => alert('此处会跳转到人物库详情页面')}>
                          <Eye className="h-4 w-4 mr-2" /> 查看
                        </Button>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="col-span-full text-center py-10 text-muted-foreground">
                    暂无人物库，请点击右上角"新建人物库"按钮创建
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Add Library Dialog */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>新建人物库</DialogTitle>
              <DialogDescription>
                创建一个新的人物库，用于管理人物特征识别信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">人物库名称</Label>
                <Input
                  id="name"
                  placeholder="输入人物库名称"
                  value={newLibraryName}
                  onChange={(e) => setNewLibraryName(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  placeholder="输入人物库描述..."
                  value={newLibraryDescription}
                  onChange={(e) => setNewLibraryDescription(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
              <Button onClick={handleAddLibrary}>创建</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Library Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>编辑人物库</DialogTitle>
              <DialogDescription>
                修改人物库的名称和描述信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">人物库名称</Label>
                <Input
                  id="edit-name"
                  placeholder="输入人物库名称"
                  value={newLibraryName}
                  onChange={(e) => setNewLibraryName(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">描述</Label>
                <Textarea
                  id="edit-description"
                  placeholder="输入人物库描述..."
                  value={newLibraryDescription}
                  onChange={(e) => setNewLibraryDescription(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
              <Button onClick={handleEditLibrary}>保存</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Library Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>删除人物库</DialogTitle>
              <DialogDescription>
                确定要删除 "{selectedLibrary?.name}" 人物库吗？此操作不可撤销。
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>取消</Button>
              <Button variant="destructive" onClick={handleDeleteLibrary}>确认删除</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  );
};

export default PeoplePage;
