import { createRoot } from 'react-dom/client'
import './index.css'

// 根据环境变量加载不同的应用
const isPublishing = import.meta.env.VITE_APP_TYPE === 'publishing';

console.log('=== main.tsx ===');
console.log('Environment variables:', import.meta.env);
console.log('VITE_APP_TYPE =', import.meta.env.VITE_APP_TYPE);
console.log('isPublishing =', isPublishing);
console.log('================');

// 动态导入相应的应用
if (isPublishing) {
  console.log('🚀 Loading PublishingApp...');
  import('./publishing/PublishingApp').then(({ PublishingApp }) => {
    console.log('✅ PublishingApp loaded successfully');
    createRoot(document.getElementById("root")!).render(<PublishingApp />);
  }).catch(error => {
    console.error('❌ Failed to load PublishingApp:', error);
  });
} else {
  console.log('🚀 Loading App...');
  import('./App').then((module) => {
    console.log('✅ App loaded successfully');
    const App = module.default;
    createRoot(document.getElementById("root")!).render(<App />);
  }).catch(error => {
    console.error('❌ Failed to load App:', error);
  });
}
