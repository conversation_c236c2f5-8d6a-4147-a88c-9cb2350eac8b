import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";

// Import publishing system pages
import PublishingLogin from "./pages/publishing/PublishingLogin";
import PublishingHomePage from "./pages/publishing/PublishingHomePage";
import PublishingSearchResultsPage from "./pages/publishing/PublishingSearchResultsPage";
import PublishingDetailPage from "./pages/publishing/PublishingDetailPage";
import NotFoundPage from "./pages/NotFoundPage";

const queryClient = new QueryClient();

const PublishingApp = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider isPublishingSystem={true}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<PublishingLogin />} />
            <Route path="/" element={<PublishingHomePage />} />
            <Route path="/search" element={<PublishingSearchResultsPage />} />
            <Route path="/detail/:id" element={<PublishingDetailPage />} />
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default PublishingApp;
