import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  // 检查是否是发布子系统
  const isPublishing = process.env.VITE_APP_TYPE === 'publishing';

  console.log('vite.config.ts: isPublishing =', isPublishing);

  return {
    server: {
      host: "::",
      port: isPublishing ? 8081 : 8080,
    },
    plugins: [
      react(),
      mode === 'development' &&
      componentTagger(),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@publishing": path.resolve(__dirname, "./src/publishing"),
        "@management": path.resolve(__dirname, "./src/management"),
      },
    },
    build: {
      rollupOptions: {
        input: {
          main: path.resolve(__dirname, isPublishing ? 'publishing.html' : 'index.html'),
        },
      },
    },
  };
});
