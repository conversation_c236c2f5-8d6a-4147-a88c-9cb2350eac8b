
import { VideoMaterial, SearchFilters, VideoSource, MediaType, ProcessingStatus, CatalogStatus, AuditStatus, PublishStatus, FeatureType, ChatMessage } from "@/types";

// Mock data generator
const generateMockVideos = (count: number): VideoMaterial[] => {
  const mockVideos: VideoMaterial[] = [];
  
  const sources = Object.values(VideoSource);
  const types = Object.values(MediaType);
  const processingStatuses = Object.values(ProcessingStatus);
  const catalogStatuses = Object.values(CatalogStatus);
  const auditStatuses = Object.values(AuditStatus);
  
  for (let i = 0; i < count; i++) {
    const id = `video-${i + 1}`;
    const randomSource = sources[Math.floor(Math.random() * sources.length)];
    const randomType = types[Math.floor(Math.random() * types.length)];
    const randomProcessingStatus = processingStatuses[Math.floor(Math.random() * processingStatuses.length)];
    const randomCatalogStatus = catalogStatuses[Math.floor(Math.random() * catalogStatuses.length)];

    // 为前几个视频设置待审核状态，用于测试
    let randomAuditStatus = undefined;
    if (i < 5) {
      randomAuditStatus = AuditStatus.PENDING;
    } else if (i < 10) {
      randomAuditStatus = auditStatuses[Math.floor(Math.random() * auditStatuses.length)];
    }
    
    const createdDate = new Date();
    createdDate.setDate(createdDate.getDate() - Math.floor(Math.random() * 30));
    
    mockVideos.push({
      id,
      name: `视频素材 ${i + 1}`,
      source: randomSource,
      type: randomType,
      processingStatus: randomProcessingStatus,
      catalogStatus: randomCatalogStatus,
      auditStatus: randomAuditStatus,
      submittedAt: randomAuditStatus ? new Date(createdDate.getTime() + 24 * 60 * 60 * 1000).toISOString() : undefined,
      submittedBy: randomAuditStatus ? "张三" : undefined,
      createdAt: createdDate.toISOString(),
      thumbnails: [
        `https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGNvZGV8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60`,
        `https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8Y29kZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60`,
        `https://images.unsplash.com/photo-1531297484001-80022131f5a1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y29kZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60`
      ],
      coverImage: `https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGNvZGV8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60`,
      duration: Math.floor(Math.random() * 600) + 60, // 1-10 mins
      resolution: "1920x1080",
      bitrate: "2000 kbps",
      codec: "H.264",
      fileSize: `${Math.floor(Math.random() * 500) + 50} MB`,
      summary: "这是一个示例视频素材的内容摘要。该视频展示了一些重要内容，可能包含关键信息。",
      transcript: Array(10).fill(null).map((_, idx) => ({
        startTime: idx * 10,
        endTime: (idx + 1) * 10 - 0.5,
        text: `这是第 ${idx + 1} 段字幕内容，展示了视频中的对话或旁白。`
      })),
      features: [
        {
          id: `feature-face-${i}-1`,
          type: FeatureType.FACE,
          name: "某某人",
          confidence: 0.85,
          timestamp: Math.floor(Math.random() * 100),
          imageUrl: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGF2YXRhcnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60"
        },
        {
          id: `feature-marker-${i}-1`,
          type: FeatureType.MARKER,
          name: "某标志物",
          confidence: 0.75,
          timestamp: Math.floor(Math.random() * 100),
          imageUrl: "https://images.unsplash.com/photo-1531297484001-80022131f5a1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Y29kZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60"
        },
        {
          id: `feature-keyword-${i}-1`,
          type: FeatureType.KEYWORD,
          name: "敏感词语",
          confidence: 0.95,
          timestamp: Math.floor(Math.random() * 100)
        }
      ],
      catalogInfo: {
        "标题": `视频素材 ${i + 1}`,
        "类别": "新闻",
        "关键词": "示例,测试,演示",
        "简介": "这是一个示例视频素材的简介信息。",
        "地点": "北京市",
        "时间": new Date().toLocaleDateString()
      }
    });
  }
  
  return mockVideos;
};

// Generate mock data
const mockVideos = generateMockVideos(50);

export const api = {
  // 素材库 API
  async getMaterialList(filters?: SearchFilters): Promise<VideoMaterial[]> {
    // Simulate API call delay
    await new Promise(r => setTimeout(r, 500));
    
    // Filter the mock videos based on the provided filters
    let filteredVideos = [...mockVideos];
    
    if (filters) {
      if (filters.name) {
        filteredVideos = filteredVideos.filter(v => 
          v.name.toLowerCase().includes(filters.name!.toLowerCase())
        );
      }
      
      if (filters.source) {
        filteredVideos = filteredVideos.filter(v => v.source === filters.source);
      }
      
      if (filters.type) {
        filteredVideos = filteredVideos.filter(v => v.type === filters.type);
      }
      
      if (filters.processingStatus) {
        filteredVideos = filteredVideos.filter(v => v.processingStatus === filters.processingStatus);
      }
      
      if (filters.catalogStatus) {
        filteredVideos = filteredVideos.filter(v => v.catalogStatus === filters.catalogStatus);
      }
      
      if (filters.startDate) {
        filteredVideos = filteredVideos.filter(v => 
          new Date(v.createdAt) >= new Date(filters.startDate!)
        );
      }
      
      if (filters.endDate) {
        filteredVideos = filteredVideos.filter(v => 
          new Date(v.createdAt) <= new Date(filters.endDate!)
        );
      }
    }
    
    return filteredVideos;
  },
  
  async getMaterialById(id: string): Promise<VideoMaterial | null> {
    // Simulate API call delay
    await new Promise(r => setTimeout(r, 300));
    
    const video = mockVideos.find(v => v.id === id);
    return video || null;
  },
  
  async uploadMaterial(file: File): Promise<VideoMaterial> {
    // Simulate API call delay
    await new Promise(r => setTimeout(r, 2000));
    
    // Create a new mock video
    const newVideo: VideoMaterial = {
      id: `video-${mockVideos.length + 1}`,
      name: file.name,
      source: VideoSource.UPLOAD,
      type: file.type.includes("video") ? MediaType.VIDEO : MediaType.AUDIO,
      processingStatus: ProcessingStatus.PENDING,
      catalogStatus: CatalogStatus.PENDING,
      createdAt: new Date().toISOString(),
      thumbnails: [
        `https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGNvZGV8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60`
      ],
      coverImage: `https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGNvZGV8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60`,
      duration: 180,
      resolution: "1920x1080",
      bitrate: "2000 kbps",
      codec: "H.264",
      fileSize: `${Math.floor(file.size / 1024 / 1024)} MB`,
      summary: "新上传的视频，等待处理和编目。",
    };
    
    // Add to mock videos
    mockVideos.unshift(newVideo);
    
    return newVideo;
  },
  
  async updateMaterial(id: string, data: Partial<VideoMaterial>): Promise<VideoMaterial> {
    // Simulate API call delay
    await new Promise(r => setTimeout(r, 300));
    
    const index = mockVideos.findIndex(v => v.id === id);
    if (index === -1) {
      throw new Error("视频素材不存在");
    }
    
    const updatedVideo = {
      ...mockVideos[index],
      ...data,
    };
    
    mockVideos[index] = updatedVideo;
    
    return updatedVideo;
  },
  
  async deleteMaterial(id: string): Promise<void> {
    // Simulate API call delay
    await new Promise(r => setTimeout(r, 300));
    
    const index = mockVideos.findIndex(v => v.id === id);
    if (index === -1) {
      throw new Error("视频素材不存在");
    }
    
    mockVideos.splice(index, 1);
  },
  
  async submitForAudit(id: string): Promise<VideoMaterial> {
    // Simulate API call delay
    await new Promise(r => setTimeout(r, 300));
    
    const index = mockVideos.findIndex(v => v.id === id);
    if (index === -1) {
      throw new Error("视频素材不存在");
    }
    
    const updatedVideo = {
      ...mockVideos[index],
      auditStatus: AuditStatus.PENDING,
      submittedAt: new Date().toISOString(),
      submittedBy: "admin",
    };
    
    mockVideos[index] = updatedVideo;
    
    return updatedVideo;
  },
  
  // AI Assistant API
  async chatWithAI(videoId: string, message: string): Promise<ChatMessage> {
    // Simulate API call delay
    await new Promise(r => setTimeout(r, 800));
    
    // Generate a mock response based on the message
    const responses = [
      "这个视频主要展示了一些技术演示的内容，包括代码示例和界面展示。",
      "视频中确实提到了您询问的内容，约在视频的2分30秒处有详细说明。",
      "根据视频分析，这段内容属于教育类视频，主要面向技术学习者。",
      "视频中没有检测到敏感内容，可以安全使用。",
      "根据分析，视频中的说话者是一位技术讲师，正在讲解编程相关内容。",
      "视频质量良好，音频清晰，没有检测到明显的质量问题。",
      "视频中确实包含了您提到的技术点，主要在视频的后半部分有详细解释。",
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    return {
      id: `msg-${Date.now()}`,
      role: 'assistant',
      content: randomResponse,
      timestamp: Date.now(),
    };
  },
  
  async generateDocument(videoId: string): Promise<string> {
    // Simulate API call delay
    await new Promise(r => setTimeout(r, 2000));

    return "已生成文档，可在系统中查看和下载。";
  },

  // 审核相关 API
  async auditMaterial(id: string, status: AuditStatus, reason?: string): Promise<VideoMaterial> {
    // Simulate API call delay
    await new Promise(r => setTimeout(r, 500));

    const index = mockVideos.findIndex(v => v.id === id);
    if (index === -1) {
      throw new Error("视频素材不存在");
    }

    const updatedVideo = {
      ...mockVideos[index],
      auditStatus: status,
      auditedAt: new Date().toISOString(),
      auditedBy: "admin",
      auditReason: reason,
    };

    // 根据审核结果设置发布状态
    if (status === AuditStatus.APPROVED) {
      updatedVideo.publishStatus = PublishStatus.PUBLISHED;
    } else if (status === AuditStatus.REJECTED) {
      // 审核不通过的素材退回到素材库，清除审核状态
      updatedVideo.auditStatus = undefined;
      updatedVideo.auditedAt = undefined;
      updatedVideo.auditedBy = undefined;
    }

    mockVideos[index] = updatedVideo;

    return updatedVideo;
  }
};
