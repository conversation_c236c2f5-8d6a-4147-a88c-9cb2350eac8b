
// User related types
export interface User {
  id: string;
  username: string;
  role: string;
  department?: string;
  email?: string;
  phone?: string;
  avatar?: string;
}

// Authentication related types
export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

// Content management related types
export enum VideoSource {
  UPLOAD = "上传",
  COLLECTION = "收录",
  IMPORT = "导入"
}

export enum MediaType {
  VIDEO = "视频",
  AUDIO = "音频"
}

export enum ProcessingStatus {
  PENDING = "待处理",
  PROCESSING = "处理中",
  COMPLETED = "已完成",
  FAILED = "处理失败"
}

export enum CatalogStatus {
  PENDING = "待编目",
  IN_PROGRESS = "编目中",
  COMPLETED = "已编目"
}

export enum AuditStatus {
  PENDING = "待审核",
  APPROVED = "已通过",
  REJECTED = "未通过"
}

export enum PublishStatus {
  PUBLISHED = "已发布",
  UNPUBLISHED = "未发布"
}

// Video material interface
export interface VideoMaterial {
  id: string;
  name: string;
  source: VideoSource;
  type: MediaType;
  catalogStatus?: CatalogStatus;
  processingStatus?: ProcessingStatus;
  auditStatus?: AuditStatus;
  publishStatus?: PublishStatus;
  createdAt: string;
  submittedAt?: string;
  auditedAt?: string;
  submittedBy?: string;
  auditedBy?: string;
  thumbnails?: string[];
  coverImage?: string;
  duration?: string | number;
  resolution?: string;
  bitrate?: string;
  codec?: string;
  format?: string;
  fileSize?: string;
  description?: string;
  summary?: string;
  transcript?: Transcript[] | { time: string; text: string }[];
  features?: FeatureInfo[] | { type: string; name: string; time: string }[];
  catalogInfo?: Record<string, string>;
}

// Transcript interface
export interface Transcript {
  startTime: number;
  endTime: number;
  text: string;
}

// Feature types
export enum FeatureType {
  FACE = "敏感人脸",
  MARKER = "敏感标志物",
  KEYWORD = "敏感词"
}

// Feature info interface
export interface FeatureInfo {
  id: string;
  type: FeatureType;
  name: string;
  confidence: number;
  timestamp: number;
  imageUrl?: string;
  boundingBox?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// AI Chat Message
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

// Search filters
export interface SearchFilters {
  name?: string;
  source?: VideoSource;
  type?: MediaType;
  processingStatus?: ProcessingStatus;
  catalogStatus?: CatalogStatus;
  auditStatus?: AuditStatus;
  publishStatus?: PublishStatus;
  startDate?: string;
  endDate?: string;
  submittedBy?: string;
}
