
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Save } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";

// Mock data for roles
interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
}

const mockRoles: Role[] = [
  {
    id: "role-1",
    name: "超级管理员",
    description: "拥有系统所有权限",
    permissions: ["all"],
    userCount: 2,
  },
  {
    id: "role-2",
    name: "编目员",
    description: "负责内容编目操作",
    permissions: ["materials:read", "materials:catalog"],
    userCount: 8,
  },
  {
    id: "role-3",
    name: "审核员",
    description: "负责内容审核操作",
    permissions: ["materials:read", "audits:read", "audits:approve"],
    userCount: 5,
  },
  {
    id: "role-4",
    name: "发布员",
    description: "负责内容发布操作",
    permissions: ["materials:read", "published:read", "published:manage"],
    userCount: 3,
  },
];

const PermissionsSettingsPage = () => {
  const [roles] = useState<Role[]>(mockRoles);

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">权限管理</h1>
        
        <Card>
          <CardHeader>
            <CardTitle>权限管理</CardTitle>
            <CardDescription>管理各角色的系统权限</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>选择角色</CardTitle>
                </CardHeader>
                <CardContent>
                  <Select defaultValue="role-1">
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="选择角色" />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map(role => (
                        <SelectItem key={role.id} value={role.id}>{role.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>功能权限</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base">内容管理</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor="materials-read">素材库查看</Label>
                              <Switch id="materials-read" defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="materials-edit">素材库编辑</Label>
                              <Switch id="materials-edit" defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="materials-delete">素材库删除</Label>
                              <Switch id="materials-delete" />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="audits-manage">审核管理</Label>
                              <Switch id="audits-manage" />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="published-manage">发布管理</Label>
                              <Switch id="published-manage" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base">特征库</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor="people-read">人物库查看</Label>
                              <Switch id="people-read" defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="people-manage">人物库管理</Label>
                              <Switch id="people-manage" />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="markers-read">标记库查看</Label>
                              <Switch id="markers-read" defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="markers-manage">标记库管理</Label>
                              <Switch id="markers-manage" />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="keywords-read">敏感词库查看</Label>
                              <Switch id="keywords-read" defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="keywords-manage">敏感词库管理</Label>
                              <Switch id="keywords-manage" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-base">系统管理</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor="analytics-access">统计分析</Label>
                              <Switch id="analytics-access" defaultChecked />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="logs-access">日志管理</Label>
                              <Switch id="logs-access" />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="settings-access">系统设置</Label>
                              <Switch id="settings-access" />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor="users-manage">用户管理</Label>
                              <Switch id="users-manage" />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button className="flex items-center">
                    <Save className="mr-2 h-4 w-4" />
                    保存权限设置
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default PermissionsSettingsPage;
