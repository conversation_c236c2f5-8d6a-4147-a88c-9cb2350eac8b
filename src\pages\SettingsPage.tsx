
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { NavLink } from "react-router-dom";
import { Database, FileText, Shield, Cog, Users } from "lucide-react";

interface SettingsCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  to: string;
}

const SettingsCard = ({ icon, title, description, to }: SettingsCardProps) => (
  <NavLink to={to} className="block">
    <Card className="transition-all hover:bg-accent/50 cursor-pointer">
      <CardHeader className="flex flex-row items-center gap-4">
        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary">
          {icon}
        </div>
        <div>
          <CardTitle className="text-lg">{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
      </CardHeader>
    </Card>
  </NavLink>
);

const SettingsPage = () => {
  const settingsOptions = [
    {
      icon: <Database className="w-6 h-6" />,
      title: "转码模板",
      description: "管理视频转码的默认输出格式",
      to: "/settings/transcode"
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: "编目模板",
      description: "管理可用于编目的字段和模板",
      to: "/settings/catalog"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "角色管理",
      description: "创建和管理系统角色",
      to: "/settings/roles"
    },
    {
      icon: <Cog className="w-6 h-6" />,
      title: "权限管理",
      description: "设置角色的功能权限",
      to: "/settings/permissions"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "用户管理",
      description: "管理系统用户及其角色分配",
      to: "/settings/users"
    }
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">系统设置</h1>
          <p className="text-muted-foreground">管理系统的配置和设置</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {settingsOptions.map((option, index) => (
            <SettingsCard 
              key={index} 
              icon={option.icon} 
              title={option.title} 
              description={option.description} 
              to={option.to} 
            />
          ))}
        </div>
      </div>
    </AppLayout>
  );
};

export default SettingsPage;
