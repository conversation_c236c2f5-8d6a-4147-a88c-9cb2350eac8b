
import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileVideo, CheckCircle, Upload, Trash2 } from "lucide-react";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, Legend } from "recharts";

const DashboardPage = () => {
  const [stats, setStats] = useState({
    totalMaterials: 0,
    pendingAudit: 0,
    published: 0,
    trashed: 0,
  });

  // Mock data for charts
  const dailyStats = [
    { name: '周一', 上传: 4, 收录: 2, 导入: 1 },
    { name: '周二', 上传: 3, 收录: 1, 导入: 2 },
    { name: '周三', 上传: 2, 收录: 3, 导入: 3 },
    { name: '周四', 上传: 5, 收录: 2, 导入: 1 },
    { name: '周五', 上传: 6, 收录: 4, 导入: 0 },
    { name: '周六', 上传: 2, 收录: 1, 导入: 1 },
    { name: '周日', 上传: 1, 收录: 2, 导入: 0 },
  ];

  const processingStats = [
    { name: '素材上传', 已完成: 25, 处理中: 5, 待处理: 10 },
    { name: '素材编目', 已完成: 20, 处理中: 8, 待处理: 12 },
    { name: '素材审核', 已完成: 15, 处理中: 5, 待处理: 20 },
    { name: '素材发布', 已完成: 12, 处理中: 3, 待处理: 25 },
  ];

  // Simulate loading dashboard data
  useEffect(() => {
    const loadStats = async () => {
      // In a real app, fetch data from API
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      setStats({
        totalMaterials: 58,
        pendingAudit: 15,
        published: 12,
        trashed: 3,
      });
    };
    
    loadStats();
  }, []);

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">数据统计</h1>
        
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="glass-morphism animate-fade-in">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 rounded-md bg-blue-500/20 mr-4">
                  <FileVideo className="h-8 w-8 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">素材总量</p>
                  <h3 className="text-2xl font-bold">{stats.totalMaterials}</h3>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="glass-morphism animate-fade-in [animation-delay:150ms]">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 rounded-md bg-yellow-500/20 mr-4">
                  <CheckCircle className="h-8 w-8 text-yellow-500" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">待审核素材</p>
                  <h3 className="text-2xl font-bold">{stats.pendingAudit}</h3>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="glass-morphism animate-fade-in [animation-delay:300ms]">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 rounded-md bg-green-500/20 mr-4">
                  <Upload className="h-8 w-8 text-green-500" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">已发布素材</p>
                  <h3 className="text-2xl font-bold">{stats.published}</h3>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="glass-morphism animate-fade-in [animation-delay:450ms]">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 rounded-md bg-red-500/20 mr-4">
                  <Trash2 className="h-8 w-8 text-red-500" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">回收站</p>
                  <h3 className="text-2xl font-bold">{stats.trashed}</h3>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Daily Stats Chart */}
        <Card className="glass-morphism">
          <CardHeader>
            <CardTitle>每日素材统计</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={dailyStats}>
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis dataKey="name" stroke="#888" />
                <YAxis stroke="#888" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#222', 
                    border: '1px solid #444',
                    borderRadius: '4px',
                    color: '#fff'
                  }} 
                />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="上传" 
                  stroke="#9b87f5" 
                  strokeWidth={2} 
                  activeDot={{ r: 8 }} 
                />
                <Line 
                  type="monotone" 
                  dataKey="收录" 
                  stroke="#1EAEDB" 
                  strokeWidth={2} 
                />
                <Line 
                  type="monotone" 
                  dataKey="导入" 
                  stroke="#D6BCFA" 
                  strokeWidth={2} 
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        {/* Processing Stats Chart */}
        <Card className="glass-morphism">
          <CardHeader>
            <CardTitle>素材处理状态统计</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={processingStats}>
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis dataKey="name" stroke="#888" />
                <YAxis stroke="#888" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#222', 
                    border: '1px solid #444',
                    borderRadius: '4px',
                    color: '#fff'
                  }} 
                />
                <Legend />
                <Bar dataKey="已完成" stackId="a" fill="#32CD32" />
                <Bar dataKey="处理中" stackId="a" fill="#FFD700" />
                <Bar dataKey="待处理" stackId="a" fill="#FF6347" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default DashboardPage;
