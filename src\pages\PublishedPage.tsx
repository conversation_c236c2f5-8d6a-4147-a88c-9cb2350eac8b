
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AuditStatus, MediaType, PublishStatus, VideoMaterial, VideoSource } from "@/types";
import { Ban, Eye } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useNavigate } from "react-router-dom";
import { toast } from "@/hooks/use-toast";

// Mock data for demonstration
const mockPublishedItems: VideoMaterial[] = [
  {
    id: "pub-1",
    name: "公司年会宣传视频",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-12",
    submittedAt: "2023-05-13",
    auditedAt: "2023-05-14",
    submittedBy: "张三",
    auditedBy: "李四",
    thumbnails: ["/placeholder.svg"],
  },
  {
    id: "pub-2",
    name: "产品演示视频",
    source: VideoSource.COLLECTION,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-10",
    submittedAt: "2023-05-11",
    auditedAt: "2023-05-12",
    submittedBy: "王五",
    auditedBy: "赵六",
    thumbnails: ["/placeholder.svg"],
  },
];

const PublishedPage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [sourceFilter, setSourceFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [publishFilter, setPublishFilter] = useState<string>("all");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const navigate = useNavigate();

  // Filter logic
  const filteredItems = mockPublishedItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesSource = sourceFilter === "all" || item.source === sourceFilter;
    const matchesType = typeFilter === "all" || item.type === typeFilter;
    const matchesPublish = publishFilter === "all" || item.publishStatus === publishFilter;

    return matchesSearch && matchesSource && matchesType && matchesPublish;
  });

  const handleUnpublish = (id: string) => {
    setSelectedItemId(id);
    setIsDialogOpen(true);
  };

  const confirmUnpublish = () => {
    // In a real app, this would call an API to update the status
    toast({
      title: "操作成功",
      description: "内容已设置为未发布状态",
    });
    setIsDialogOpen(false);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">发布库</h1>
        <Card>
          <CardHeader>
            <CardTitle>已发布内容</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <Input
                    placeholder="搜索名称..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="w-[150px]">
                  <Select value={sourceFilter} onValueChange={setSourceFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="来源筛选" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部来源</SelectItem>
                      <SelectItem value={VideoSource.UPLOAD}>上传</SelectItem>
                      <SelectItem value={VideoSource.COLLECTION}>收录</SelectItem>
                      <SelectItem value={VideoSource.IMPORT}>导入</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-[150px]">
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="类型筛选" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value={MediaType.VIDEO}>视频</SelectItem>
                      <SelectItem value={MediaType.AUDIO}>音频</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-[150px]">
                  <Select value={publishFilter} onValueChange={setPublishFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="发布状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value={PublishStatus.PUBLISHED}>已发布</SelectItem>
                      <SelectItem value={PublishStatus.UNPUBLISHED}>未发布</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>名称</TableHead>
                    <TableHead>来源</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>提交人</TableHead>
                    <TableHead>审核人</TableHead>
                    <TableHead>审核时间</TableHead>
                    <TableHead>发布状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredItems.length > 0 ? (
                    filteredItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.source}</TableCell>
                        <TableCell>{item.type}</TableCell>
                        <TableCell>{item.submittedBy || "-"}</TableCell>
                        <TableCell>{item.auditedBy || "-"}</TableCell>
                        <TableCell>{item.auditedAt || "-"}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            item.publishStatus === PublishStatus.PUBLISHED ? 'bg-green-200 text-green-800' : 
                            'bg-gray-200 text-gray-800'
                          }`}>
                            {item.publishStatus}
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="mr-2"
                            onClick={() => navigate(`/materials/${item.id}`)}
                          >
                            <Eye className="h-4 w-4 mr-1" /> 查看
                          </Button>
                          {item.publishStatus === PublishStatus.PUBLISHED && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleUnpublish(item.id)}
                            >
                              <Ban className="h-4 w-4 mr-1" /> 取消发布
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-4">
                        暂无符合条件的发布内容
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认取消发布</AlertDialogTitle>
              <AlertDialogDescription>
                确定要将此内容设置为未发布状态吗？取消发布后，内容将不会在发布系统中显示。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction onClick={confirmUnpublish}>确认</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppLayout>
  );
};

export default PublishedPage;
