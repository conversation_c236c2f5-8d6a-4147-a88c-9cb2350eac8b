import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/context/AuthContext";

// Import publishing system pages
import PublishingLogin from "@/publishing/pages/PublishingLogin";
import PublishingHomePage from "@/publishing/pages/PublishingHomePage";
import NotFoundPage from "@/pages/NotFoundPage";
// 暂时使用原始路径，直到我们完成所有页面的迁移
import PublishingSearchResultsPage from "@/pages/publishing/PublishingSearchResultsPage";
import PublishingDetailPage from "@/pages/publishing/PublishingDetailPage";

const queryClient = new QueryClient();

export const PublishingApp = () => {
  console.log('=== PublishingApp.tsx ===');
  console.log('Rendering PublishingApp component');
  console.log('PublishingLogin component:', PublishingLogin);
  console.log('PublishingHomePage component:', PublishingHomePage);
  console.log('========================');

  // 确保全局变量已设置
  if (typeof window !== 'undefined') {
    (window as any).IS_PUBLISHING_SYSTEM = true;
    console.log('PublishingApp: Setting window.IS_PUBLISHING_SYSTEM =', (window as any).IS_PUBLISHING_SYSTEM);
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider isPublishingSystem={true}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/login" element={
                (() => {
                  console.log('Rendering PublishingLogin route');
                  return <PublishingLogin />;
                })()
              } />
              <Route path="/" element={
                (() => {
                  console.log('Rendering PublishingHomePage route');
                  return <PublishingHomePage />;
                })()
              } />
              <Route path="/search" element={<PublishingSearchResultsPage />} />
              <Route path="/detail/:id" element={<PublishingDetailPage />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};
