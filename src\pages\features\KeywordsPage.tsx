
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Edit, Trash, Plus, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";

// Mock data structure for keyword libraries
interface KeywordLibrary {
  id: string;
  name: string;
  description: string;
  keywords: string[];
  createdAt: string;
}

// Mock data
const mockKeywordLibraries: KeywordLibrary[] = [
  {
    id: "kl-1",
    name: "政治敏感词",
    description: "政治相关敏感词库",
    keywords: ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"],
    createdAt: "2023-01-10",
  },
  {
    id: "kl-2",
    name: "暴力词汇",
    description: "包含暴力、恐怖等相关敏感词",
    keywords: ["关键词A", "关键词B", "关键词C"],
    createdAt: "2023-02-15",
  },
  {
    id: "kl-3",
    name: "色情词汇",
    description: "包含低俗、色情等相关敏感词",
    keywords: ["关键词X", "关键词Y", "关键词Z", "关键词W"],
    createdAt: "2023-03-20",
  },
];

const KeywordsPage = () => {
  const [libraries, setLibraries] = useState<KeywordLibrary[]>(mockKeywordLibraries);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isKeywordsDialogOpen, setIsKeywordsDialogOpen] = useState(false);
  const [newLibraryName, setNewLibraryName] = useState("");
  const [newLibraryDescription, setNewLibraryDescription] = useState("");
  const [selectedLibrary, setSelectedLibrary] = useState<KeywordLibrary | null>(null);
  const [newKeyword, setNewKeyword] = useState("");
  const [keywordsList, setKeywordsList] = useState<string[]>([]);

  // Filter libraries based on search query
  const filteredLibraries = libraries.filter(library =>
    library.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    library.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddLibrary = () => {
    if (!newLibraryName.trim()) {
      toast({
        title: "错误",
        description: "敏感词库名称不能为空",
        variant: "destructive",
      });
      return;
    }

    const newLibrary: KeywordLibrary = {
      id: `kl-${Date.now()}`,
      name: newLibraryName,
      description: newLibraryDescription,
      keywords: [],
      createdAt: new Date().toISOString().split('T')[0],
    };

    setLibraries([...libraries, newLibrary]);
    setNewLibraryName("");
    setNewLibraryDescription("");
    setIsAddDialogOpen(false);
    
    toast({
      title: "创建成功",
      description: `敏感词库 "${newLibraryName}" 已成功创建`,
    });
  };

  const handleEditLibrary = () => {
    if (!selectedLibrary) return;
    
    if (!newLibraryName.trim()) {
      toast({
        title: "错误",
        description: "敏感词库名称不能为空",
        variant: "destructive",
      });
      return;
    }

    const updatedLibraries = libraries.map(lib => 
      lib.id === selectedLibrary.id 
        ? { ...lib, name: newLibraryName, description: newLibraryDescription } 
        : lib
    );

    setLibraries(updatedLibraries);
    setIsEditDialogOpen(false);
    
    toast({
      title: "修改成功",
      description: `敏感词库 "${newLibraryName}" 已更新`,
    });
  };

  const handleDeleteLibrary = () => {
    if (!selectedLibrary) return;

    const updatedLibraries = libraries.filter(lib => lib.id !== selectedLibrary.id);
    setLibraries(updatedLibraries);
    setIsDeleteDialogOpen(false);
    
    toast({
      title: "删除成功",
      description: `敏感词库 "${selectedLibrary.name}" 已删除`,
      variant: "destructive",
    });
  };

  const openEditDialog = (library: KeywordLibrary) => {
    setSelectedLibrary(library);
    setNewLibraryName(library.name);
    setNewLibraryDescription(library.description);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (library: KeywordLibrary) => {
    setSelectedLibrary(library);
    setIsDeleteDialogOpen(true);
  };

  const openKeywordsDialog = (library: KeywordLibrary) => {
    setSelectedLibrary(library);
    setKeywordsList([...library.keywords]);
    setIsKeywordsDialogOpen(true);
  };

  const handleAddKeyword = () => {
    if (!newKeyword.trim()) return;

    if (keywordsList.includes(newKeyword.trim())) {
      toast({
        title: "错误",
        description: "敏感词已存在",
        variant: "destructive",
      });
      return;
    }

    setKeywordsList([...keywordsList, newKeyword.trim()]);
    setNewKeyword("");
  };

  const handleRemoveKeyword = (keyword: string) => {
    setKeywordsList(keywordsList.filter(k => k !== keyword));
  };

  const handleSaveKeywords = () => {
    if (!selectedLibrary) return;

    const updatedLibraries = libraries.map(lib => 
      lib.id === selectedLibrary.id 
        ? { ...lib, keywords: keywordsList } 
        : lib
    );

    setLibraries(updatedLibraries);
    setIsKeywordsDialogOpen(false);
    
    toast({
      title: "保存成功",
      description: `敏感词库 "${selectedLibrary.name}" 的敏感词已更新`,
    });
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">敏感词库</h1>
          <Button 
            className="flex items-center gap-2"
            onClick={() => {
              setNewLibraryName("");
              setNewLibraryDescription("");
              setIsAddDialogOpen(true);
            }}
          >
            <PlusCircle className="w-4 h-4" />
            新建敏感词库
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>敏感词库列表</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex w-full max-w-sm">
                <Input
                  placeholder="搜索敏感词库..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>名称</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>敏感词数量</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLibraries.length > 0 ? (
                    filteredLibraries.map((library) => (
                      <TableRow key={library.id}>
                        <TableCell className="font-medium">{library.name}</TableCell>
                        <TableCell>{library.description}</TableCell>
                        <TableCell>{library.keywords.length}</TableCell>
                        <TableCell>{library.createdAt}</TableCell>
                        <TableCell className="text-right">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="mr-2"
                            onClick={() => openKeywordsDialog(library)}
                          >
                            <Plus className="h-4 w-4 mr-1" /> 编辑词库
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="mr-1"
                            onClick={() => openEditDialog(library)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => openDeleteDialog(library)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4">
                        暂无敏感词库，请点击右上角"新建敏感词库"按钮创建
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Add Library Dialog */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>新建敏感词库</DialogTitle>
              <DialogDescription>
                创建一个新的敏感词库，用于管理敏感词识别信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">敏感词库名称</Label>
                <Input
                  id="name"
                  placeholder="输入敏感词库名称"
                  value={newLibraryName}
                  onChange={(e) => setNewLibraryName(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  placeholder="输入敏感词库描述..."
                  value={newLibraryDescription}
                  onChange={(e) => setNewLibraryDescription(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
              <Button onClick={handleAddLibrary}>创建</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Library Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>编辑敏感词库</DialogTitle>
              <DialogDescription>
                修改敏感词库的名称和描述信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">敏感词库名称</Label>
                <Input
                  id="edit-name"
                  placeholder="输入敏感词库名称"
                  value={newLibraryName}
                  onChange={(e) => setNewLibraryName(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">描述</Label>
                <Textarea
                  id="edit-description"
                  placeholder="输入敏感词库描述..."
                  value={newLibraryDescription}
                  onChange={(e) => setNewLibraryDescription(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
              <Button onClick={handleEditLibrary}>保存</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Library Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>删除敏感词库</DialogTitle>
              <DialogDescription>
                确定要删除 "{selectedLibrary?.name}" 敏感词库吗？此操作不可撤销。
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>取消</Button>
              <Button variant="destructive" onClick={handleDeleteLibrary}>确认删除</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Keywords Dialog */}
        <Dialog open={isKeywordsDialogOpen} onOpenChange={setIsKeywordsDialogOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>编辑敏感词</DialogTitle>
              <DialogDescription>
                管理 "{selectedLibrary?.name}" 敏感词库的敏感词列表
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <div className="flex gap-2 mb-4">
                <Input
                  placeholder="输入新的敏感词"
                  value={newKeyword}
                  onChange={(e) => setNewKeyword(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddKeyword();
                    }
                  }}
                  className="flex-1"
                />
                <Button onClick={handleAddKeyword}>添加</Button>
              </div>
              
              <div className="border rounded-md p-4 min-h-[100px] max-h-[300px] overflow-y-auto">
                {keywordsList.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {keywordsList.map((keyword, index) => (
                      <Badge key={index} variant="secondary" className="px-3 py-1 text-sm">
                        {keyword}
                        <X 
                          className="ml-2 h-3 w-3 cursor-pointer hover:text-destructive" 
                          onClick={() => handleRemoveKeyword(keyword)}
                        />
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-muted-foreground">暂无敏感词，请添加</p>
                )}
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsKeywordsDialogOpen(false)}>取消</Button>
              <Button onClick={handleSaveKeywords}>保存</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  );
};

export default KeywordsPage;
