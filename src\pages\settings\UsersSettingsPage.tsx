
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { PlusCircle, Pencil } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";

// Mock data for users
interface User {
  id: string;
  username: string;
  department: string;
  role: string;
  phone?: string;
  email?: string;
  isActive: boolean;
}

const mockUsers: User[] = [
  {
    id: "user-1",
    username: "admin",
    department: "系统管理部",
    role: "超级管理员",
    phone: "13800138000",
    email: "<EMAIL>",
    isActive: true,
  },
  {
    id: "user-2",
    username: "张三",
    department: "编目部",
    role: "编目员",
    phone: "13900139001",
    email: "zhang<PERSON>@example.com",
    isActive: true,
  },
  {
    id: "user-3",
    username: "李四",
    department: "审核部",
    role: "审核员",
    phone: "13800138002",
    email: "<EMAIL>",
    isActive: true,
  },
  {
    id: "user-4",
    username: "王五",
    department: "发布部",
    role: "发布员",
    phone: "13900139003",
    email: "<EMAIL>",
    isActive: false,
  },
];

const UsersSettingsPage = () => {
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [searchQuery, setSearchQuery] = useState("");

  // Filter users based on search
  const filteredUsers = users.filter(user =>
    user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.role.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (user.email && user.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (user.phone && user.phone.includes(searchQuery))
  );

  // Mock handlers
  const handleCreateUser = () => {
    toast({
      title: "创建用户",
      description: "此处将打开用户创建表单",
    });
  };

  const handleResetPassword = (userId: string) => {
    toast({
      title: "重置密码",
      description: `用户ID ${userId} 的密码已重置并发送至邮箱`,
    });
  };

  const handleToggleUserStatus = (userId: string, currentStatus: boolean) => {
    setUsers(
      users.map(user => 
        user.id === userId ? { ...user, isActive: !currentStatus } : user
      )
    );
    
    toast({
      title: currentStatus ? "禁用用户" : "启用用户",
      description: `用户 ${users.find(u => u.id === userId)?.username} 已${currentStatus ? '禁用' : '启用'}`,
    });
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">用户管理</h1>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>用户管理</CardTitle>
              <CardDescription>管理系统用户及其角色分配</CardDescription>
            </div>
            <Button onClick={handleCreateUser} className="flex items-center">
              <PlusCircle className="mr-2 h-4 w-4" />
              新建用户
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex w-full max-w-sm">
                <Input
                  placeholder="搜索用户名、部门、角色..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>用户名</TableHead>
                    <TableHead>部门</TableHead>
                    <TableHead>角色</TableHead>
                    <TableHead>手机号</TableHead>
                    <TableHead>邮箱</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length > 0 ? (
                    filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.username}</TableCell>
                        <TableCell>{user.department}</TableCell>
                        <TableCell>{user.role}</TableCell>
                        <TableCell>{user.phone || '-'}</TableCell>
                        <TableCell>{user.email || '-'}</TableCell>
                        <TableCell>
                          <Badge className={user.isActive ? 'bg-green-500' : 'bg-gray-500'}>
                            {user.isActive ? '正常' : '禁用'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="mr-1"
                            onClick={() => handleResetPassword(user.id)}
                          >
                            重置密码
                          </Button>
                          <Button 
                            variant={user.isActive ? "destructive" : "outline"}
                            size="sm"
                            className="mr-1"
                            onClick={() => handleToggleUserStatus(user.id, user.isActive)}
                          >
                            {user.isActive ? '禁用' : '启用'}
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="mr-1"
                            onClick={() => toast({ title: "编辑用户", description: `此处将打开ID为${user.id}的用户编辑表单` })}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        暂无符合条件的用户
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default UsersSettingsPage;
