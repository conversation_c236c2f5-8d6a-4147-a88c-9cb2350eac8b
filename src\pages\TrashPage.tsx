
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MediaType, VideoMaterial, VideoSource } from "@/types";
import { ArrowLeftRight, Trash2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Mock data for demonstration
const mockTrashItems: VideoMaterial[] = [
  {
    id: "trash-1",
    name: "废弃宣传视频",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    createdAt: "2023-04-12",
    thumbnails: ["/placeholder.svg"],
  },
  {
    id: "trash-2",
    name: "旧版产品演示",
    source: VideoSource.IMPORT,
    type: MediaType.AUDIO,
    createdAt: "2023-03-15",
    thumbnails: ["/placeholder.svg"],
  },
];

const TrashPage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [sourceFilter, setSourceFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [isEmptyTrashDialogOpen, setIsEmptyTrashDialogOpen] = useState(false);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  
  // Filter logic
  const filteredItems = mockTrashItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesSource = sourceFilter === "all" || item.source === sourceFilter;
    const matchesType = typeFilter === "all" || item.type === typeFilter;

    return matchesSearch && matchesSource && matchesType;
  });

  const handleRestore = (id: string) => {
    setSelectedItemId(id);
    setIsRestoreDialogOpen(true);
  };

  const confirmRestore = () => {
    // In a real app, this would call an API to restore the item
    toast({
      title: "恢复成功",
      description: "内容已恢复到素材库",
    });
    setIsRestoreDialogOpen(false);
  };

  const confirmEmptyTrash = () => {
    // In a real app, this would call an API to permanently delete all items
    toast({
      title: "清空成功",
      description: "回收站已清空",
      variant: "destructive",
    });
    setIsEmptyTrashDialogOpen(false);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">回收站</h1>
          <Button 
            variant="destructive" 
            className="flex items-center gap-2"
            onClick={() => setIsEmptyTrashDialogOpen(true)}
          >
            <Trash2 className="w-4 h-4" />
            清空回收站
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>已删除内容</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <Input
                    placeholder="搜索名称..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="w-[150px]">
                  <Select value={sourceFilter} onValueChange={setSourceFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="来源筛选" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部来源</SelectItem>
                      <SelectItem value={VideoSource.UPLOAD}>上传</SelectItem>
                      <SelectItem value={VideoSource.COLLECTION}>收录</SelectItem>
                      <SelectItem value={VideoSource.IMPORT}>导入</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-[150px]">
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="类型筛选" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value={MediaType.VIDEO}>视频</SelectItem>
                      <SelectItem value={MediaType.AUDIO}>音频</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>名称</TableHead>
                    <TableHead>来源</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>删除时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredItems.length > 0 ? (
                    filteredItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.source}</TableCell>
                        <TableCell>{item.type}</TableCell>
                        <TableCell>{item.createdAt}</TableCell>
                        <TableCell className="text-right">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleRestore(item.id)}
                          >
                            <ArrowLeftRight className="h-4 w-4 mr-1" /> 恢复
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4">
                        回收站为空
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        <AlertDialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认恢复</AlertDialogTitle>
              <AlertDialogDescription>
                确定要将此内容恢复到素材库吗？
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction onClick={confirmRestore}>确认</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        <AlertDialog open={isEmptyTrashDialogOpen} onOpenChange={setIsEmptyTrashDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认清空回收站</AlertDialogTitle>
              <AlertDialogDescription>
                此操作将永久删除回收站中的所有内容，且无法恢复。是否继续？
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction onClick={confirmEmptyTrash} className="bg-red-600 hover:bg-red-700">
                确认清空
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppLayout>
  );
};

export default TrashPage;
