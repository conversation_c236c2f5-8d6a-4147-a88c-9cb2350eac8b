
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>ir<PERSON>, <PERSON>cil, Trash } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";

// Mock data for catalog templates
interface CatalogField {
  id: string;
  name: string;
  type: 'text' | 'select' | 'checkbox' | 'date';
  options?: string[];
}

interface CatalogTemplate {
  id: string;
  name: string;
  fields: string[];
  isDefault: boolean;
}

const mockCatalogFields: CatalogField[] = [
  { id: "cf-1", name: "标题", type: "text" },
  { id: "cf-2", name: "描述", type: "text" },
  { id: "cf-3", name: "类别", type: "select", options: ["新闻", "教育", "娱乐", "科技", "其他"] },
  { id: "cf-4", name: "拍摄日期", type: "date" },
  { id: "cf-5", name: "拍摄地点", type: "text" },
  { id: "cf-6", name: "关键词", type: "text" },
  { id: "cf-7", name: "版权信息", type: "text" },
  { id: "cf-8", name: "是否公开", type: "checkbox" },
];

const mockCatalogTemplates: CatalogTemplate[] = [
  { id: "ct-1", name: "标准编目", fields: ["cf-1", "cf-2", "cf-3", "cf-6"], isDefault: true },
  { id: "ct-2", name: "详细编目", fields: ["cf-1", "cf-2", "cf-3", "cf-4", "cf-5", "cf-6", "cf-7", "cf-8"], isDefault: false },
  { id: "ct-3", name: "简易编目", fields: ["cf-1", "cf-3"], isDefault: false },
];

const CatalogSettingsPage = () => {
  const [catalogFields, setCatalogFields] = useState<CatalogField[]>(mockCatalogFields);
  const [catalogTemplates, setCatalogTemplates] = useState<CatalogTemplate[]>(mockCatalogTemplates);

  // Set a template as default
  const setDefaultTemplate = (id: string) => {
    setCatalogTemplates(
      catalogTemplates.map(template => ({
        ...template,
        isDefault: template.id === id
      }))
    );
    toast({
      title: "默认模板已更新",
      description: "编目模板设置成功",
    });
  };

  // Mock handlers
  const handleCreateTemplate = () => {
    toast({
      title: "创建模板",
      description: "此处将打开编目模板创建表单",
    });
  };

  const handleEditTemplate = (id: string) => {
    toast({
      title: "编辑模板",
      description: `此处将打开ID为${id}的编目模板编辑表单`,
    });
  };

  const handleDeleteTemplate = (id: string) => {
    toast({
      title: "删除模板",
      description: `此处将确认删除ID为${id}的编目模板`,
      variant: "destructive",
    });
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">编目模板设置</h1>
        
        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>编目项</CardTitle>
                <CardDescription>管理可用于编目模板的字段</CardDescription>
              </div>
              <Button onClick={() => toast({ title: "创建编目项", description: "此处将打开编目项创建表单" })} className="flex items-center">
                <PlusCircle className="mr-2 h-4 w-4" />
                新建编目项
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>选项</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {catalogFields.map((field) => (
                    <TableRow key={field.id}>
                      <TableCell className="font-medium">{field.name}</TableCell>
                      <TableCell>
                        {field.type === 'text' && '文本'}
                        {field.type === 'select' && '下拉选择'}
                        {field.type === 'checkbox' && '选择框'}
                        {field.type === 'date' && '日期'}
                      </TableCell>
                      <TableCell>
                        {field.options ? field.options.join(', ') : '-'}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="mr-1"
                          onClick={() => toast({ title: "编辑编目项", description: `此处将打开ID为${field.id}的编目项编辑表单` })}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => toast({ 
                            title: "删除编目项", 
                            description: `此处将确认删除ID为${field.id}的编目项`, 
                            variant: "destructive" 
                          })}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>编目模板</CardTitle>
                <CardDescription>管理视频编目的模板</CardDescription>
              </div>
              <Button onClick={() => handleCreateTemplate()} className="flex items-center">
                <PlusCircle className="mr-2 h-4 w-4" />
                新建模板
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>模板名称</TableHead>
                    <TableHead>包含字段</TableHead>
                    <TableHead>默认</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {catalogTemplates.map((template) => (
                    <TableRow key={template.id}>
                      <TableCell className="font-medium">{template.name}</TableCell>
                      <TableCell>
                        {template.fields.map(fieldId => {
                          const field = catalogFields.find(f => f.id === fieldId);
                          return field ? field.name : '';
                        }).join(', ')}
                      </TableCell>
                      <TableCell>
                        {template.isDefault ? (
                          <Badge className="bg-green-500">默认</Badge>
                        ) : (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => setDefaultTemplate(template.id)}
                          >
                            设为默认
                          </Button>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="mr-1"
                          onClick={() => handleEditTemplate(template.id)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          disabled={template.isDefault}
                          onClick={() => handleDeleteTemplate(template.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
};

export default CatalogSettingsPage;
