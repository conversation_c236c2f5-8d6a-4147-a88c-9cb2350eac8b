import { ReactNode } from "react";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { PublishingHeader } from "./PublishingHeader";

interface PublishingLayoutProps {
  children: ReactNode;
}

export const PublishingLayout = ({ children }: PublishingLayoutProps) => {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="flex flex-col min-h-screen bg-dark-background text-white">
      <PublishingHeader />
      <main className="flex-1 overflow-auto p-4 md:p-6">
        {children}
      </main>
      <footer className="py-4 text-center text-sm text-gray-500 border-t border-gray-800">
        © 2025 VisionOrb. 保留所有权利。
      </footer>
      <Toaster />
    </div>
  );
};
