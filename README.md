# VisionOrb 智能视频内容管理与发布系统

VisionOrb 是一个基于视频内容的多媒体内容管理及发布系统，由内容管理子系统和内容发布子系统组成。

## 系统架构

系统由两个子系统组成：

1. **内容管理子系统**：用于上传、处理、审核和发布视频内容
2. **内容发布子系统**：用于展示和搜索已发布的视频内容

两个子系统共用一套账号体系，但有独立的入口和端口。

## 运行方式

### 内容管理子系统

```bash
# 开发模式
npm run dev

# 构建
npm run build

# 预览构建结果
npm run preview
```

内容管理子系统默认运行在 8080 端口。

### 内容发布子系统

```bash
# 开发模式
npm run dev:publishing

# 构建
npm run build:publishing

# 预览构建结果
npm run preview:publishing
```

内容发布子系统默认运行在 8081 端口。

## 登录信息

两个子系统共用一套账号体系，可以使用以下账号登录：

- 用户名：admin
- 密码：password

## 技术栈

- TypeScript
- React
- React Router
- Tailwind CSS
- Shadcn UI
- Vite

## 目录结构

```
visionOrb-frontend/
├── src/
│   ├── components/       # 共用组件
│   ├── context/          # 上下文管理
│   ├── hooks/            # 自定义钩子
│   ├── lib/              # 工具函数
│   ├── pages/            # 页面组件
│   │   ├── publishing/   # 内容发布子系统页面
│   │   └── ...           # 内容管理子系统页面
│   ├── types/            # 类型定义
│   ├── App.tsx           # 内容管理子系统入口
│   ├── PublishingApp.tsx # 内容发布子系统入口
│   ├── main.tsx          # 内容管理子系统渲染入口
│   └── publishing.tsx    # 内容发布子系统渲染入口
├── public/               # 静态资源
├── index.html            # 内容管理子系统 HTML 模板
├── publishing.html       # 内容发布子系统 HTML 模板
├── vite.config.ts        # 内容管理子系统 Vite 配置
└── vite.publishing.config.ts # 内容发布子系统 Vite 配置
```
