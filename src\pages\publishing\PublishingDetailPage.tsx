import { useEffect, useState } from "react";
import { PublishingLayout } from "@/components/layout/PublishingLayout";
import { useParams } from "react-router-dom";
import { AuditStatus, MediaType, PublishStatus, VideoMaterial, VideoSource } from "@/types";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send } from "lucide-react";

// Mock data for demonstration
const mockPublishedItems: VideoMaterial[] = [
  {
    id: "pub-1",
    name: "公司年会宣传视频",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-12",
    submittedAt: "2023-05-13",
    auditedAt: "2023-05-14",
    submittedBy: "张三",
    auditedBy: "李四",
    thumbnails: ["/placeholder.svg", "/placeholder.svg", "/placeholder.svg"],
    description: "这是一段公司年会的宣传视频，展示了公司的文化和员工风采。",
    duration: "00:15:30",
    resolution: "1920x1080",
    bitrate: "5 Mbps",
    format: "MP4",
    fileSize: "150 MB",
    transcript: [
      { time: "00:00:10", text: "欢迎大家参加我们的年度公司年会" },
      { time: "00:00:15", text: "今年是我们公司成立的第十个年头" },
      { time: "00:00:25", text: "在过去的一年里，我们取得了许多重要的成就" },
      { time: "00:00:35", text: "这些成就离不开每一位员工的辛勤付出" },
    ],
    features: [
      { type: "face", name: "张总", time: "00:01:20" },
      { type: "face", name: "李经理", time: "00:02:45" },
      { type: "marker", name: "公司Logo", time: "00:00:05" },
      { type: "keyword", name: "年度总结", time: "00:05:30" },
    ],
  },
  {
    id: "pub-2",
    name: "产品演示视频",
    source: VideoSource.COLLECTION,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-10",
    submittedAt: "2023-05-11",
    auditedAt: "2023-05-12",
    submittedBy: "王五",
    auditedBy: "赵六",
    thumbnails: ["/placeholder.svg", "/placeholder.svg"],
    description: "这是一段产品演示视频，详细介绍了产品的功能和使用方法。",
    duration: "00:10:15",
    resolution: "1280x720",
    bitrate: "3 Mbps",
    format: "MP4",
    fileSize: "80 MB",
    transcript: [
      { time: "00:00:05", text: "今天我们将为大家介绍我们的新产品" },
      { time: "00:00:15", text: "这款产品具有多项创新功能" },
      { time: "00:00:25", text: "首先，让我们看看它的外观设计" },
      { time: "00:00:40", text: "接下来，我们将演示它的主要功能" },
    ],
    features: [
      { type: "face", name: "产品经理", time: "00:00:30" },
      { type: "marker", name: "产品Logo", time: "00:00:10" },
      { type: "keyword", name: "创新功能", time: "00:01:20" },
      { type: "keyword", name: "用户体验", time: "00:03:45" },
    ],
  },
];

// Mock chat messages
interface ChatMessage {
  role: "user" | "assistant";
  content: string;
}

const PublishingDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const [video, setVideo] = useState<VideoMaterial | null>(null);
  const [activeTab, setActiveTab] = useState("info");
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [userMessage, setUserMessage] = useState("");

  useEffect(() => {
    // Find the video by id
    const foundVideo = mockPublishedItems.find(item => item.id === id);
    if (foundVideo) {
      setVideo(foundVideo);
    }
  }, [id]);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!userMessage.trim()) return;

    // Add user message
    const newUserMessage: ChatMessage = {
      role: "user",
      content: userMessage,
    };

    setChatMessages(prev => [...prev, newUserMessage]);
    setUserMessage("");

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        role: "assistant",
        content: `关于"${userMessage}"，这个视频中提到了相关内容。您可以在视频的${Math.floor(Math.random() * 10) + 1}分钟处查看详细信息。`,
      };
      setChatMessages(prev => [...prev, aiResponse]);
    }, 1000);
  };

  if (!video) {
    return (
      <PublishingLayout>
        <div className="text-center py-12">
          <p className="text-gray-400">未找到视频内容</p>
        </div>
      </PublishingLayout>
    );
  }

  return (
    <PublishingLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">{video.name}</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            {/* Video Player */}
            <div className="aspect-video bg-gray-900 rounded-lg overflow-hidden">
              <video
                controls
                className="w-full h-full"
                poster={video.thumbnails[0]}
                src="https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4"
              >
                Your browser does not support the video tag.
              </video>
            </div>

            {/* Thumbnails - Photo Wall with Sci-Fi Effect */}
            <div className="relative aspect-video bg-gray-900 rounded-lg overflow-hidden">
              <div className="absolute inset-0 bg-gradient-radial from-primary-purple/20 to-transparent opacity-50"></div>
              <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 p-4 h-full overflow-y-auto">
                {video.thumbnails.map((thumbnail, index) => (
                  <div
                    key={index}
                    className="group relative aspect-video rounded overflow-hidden transform transition-all duration-300 hover:scale-105 hover:z-10 hover:shadow-[0_0_15px_rgba(138,43,226,0.5)]"
                  >
                    <img
                      src={thumbnail}
                      alt={`缩略图 ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center">
                      <span className="text-xs text-white pb-2">点击查看</span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="absolute inset-0 pointer-events-none border border-primary-purple/30 rounded-lg"></div>
            </div>

            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="info">基本信息</TabsTrigger>
                <TabsTrigger value="transcript">配音文稿</TabsTrigger>
                <TabsTrigger value="features">特征信息</TabsTrigger>
                <TabsTrigger value="ai">AI问答</TabsTrigger>
              </TabsList>

              <TabsContent value="info" className="space-y-4 mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>内容摘要</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>{video.description}</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>基本信息</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-400">时长</p>
                        <p>{video.duration}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">分辨率</p>
                        <p>{video.resolution}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">码率</p>
                        <p>{video.bitrate}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">格式</p>
                        <p>{video.format}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">文件大小</p>
                        <p>{video.fileSize}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-400">来源</p>
                        <p>{video.source}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="transcript" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>配音文稿</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="relative aspect-video bg-gray-900 rounded-lg overflow-hidden">
                      <div className="absolute inset-0 overflow-y-auto p-4 scrollbar-thin">
                        <div className="space-y-4">
                          {video.transcript?.map((item, index) => (
                            <div key={index} className="flex hover:bg-gray-800 p-2 rounded-md transition-colors">
                              <span className="text-gray-400 w-20 flex-shrink-0">{item.time}</span>
                              <p>{item.text}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="features" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>特征信息</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="relative aspect-video bg-gray-900 rounded-lg overflow-hidden">
                      <div className="absolute inset-0 overflow-y-auto p-4 scrollbar-thin">
                        <div className="space-y-4">
                          {video.features?.map((feature, index) => (
                            <div key={index} className="flex items-center hover:bg-gray-800 p-2 rounded-md transition-colors cursor-pointer"
                                 onClick={() => {
                                   // 在实际应用中，这里会跳转到视频的对应时间点
                                   console.log(`跳转到时间点: ${feature.time}`);
                                 }}>
                              <span className="text-gray-400 w-20 flex-shrink-0">{feature.time}</span>
                              <span className="bg-primary-purple/20 text-primary-purple px-2 py-1 rounded text-xs mr-2">
                                {feature.type === "face" ? "人脸" :
                                 feature.type === "marker" ? "标志物" : "关键词"}
                              </span>
                              <p>{feature.name}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="ai" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>AI问答</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="relative aspect-video bg-gray-900 rounded-lg overflow-hidden flex flex-col">
                      <div className="absolute inset-0 p-4 flex flex-col">
                        <div className="flex-1 overflow-y-auto mb-4 space-y-4 scrollbar-thin">
                          {chatMessages.length === 0 ? (
                            <div className="flex items-center justify-center h-full">
                              <p className="text-center text-gray-400">
                                向AI提问关于这个视频的问题
                              </p>
                            </div>
                          ) : (
                            chatMessages.map((message, index) => (
                              <div
                                key={index}
                                className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                              >
                                <div
                                  className={`max-w-[80%] rounded-lg px-4 py-2 ${
                                    message.role === "user"
                                      ? "bg-primary-purple text-white"
                                      : "bg-gray-700 text-white"
                                  }`}
                                >
                                  <p>{message.content}</p>
                                </div>
                              </div>
                            ))
                          )}
                        </div>

                        <form onSubmit={handleSendMessage} className="flex gap-2">
                          <Input
                            placeholder="输入您的问题..."
                            value={userMessage}
                            onChange={(e) => setUserMessage(e.target.value)}
                            className="bg-gray-800 border-gray-700"
                          />
                          <Button type="submit" className="bg-primary-purple hover:bg-primary-dark">
                            <Send className="h-4 w-4" />
                          </Button>
                        </form>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            {/* Related Videos */}
            <Card>
              <CardHeader>
                <CardTitle>相关视频</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockPublishedItems
                    .filter(item => item.id !== video.id)
                    .map(item => (
                      <div key={item.id} className="flex space-x-3 cursor-pointer hover:bg-gray-800 p-2 rounded">
                        <div className="w-20 h-12 flex-shrink-0 rounded overflow-hidden">
                          <img
                            src={item.thumbnails[0]}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <h4 className="font-medium line-clamp-1">{item.name}</h4>
                          <p className="text-xs text-gray-400">{item.duration}</p>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PublishingLayout>
  );
};

export default PublishingDetailPage;
