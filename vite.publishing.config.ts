import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// Configuration for the publishing subsystem
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8081, // Different port from the management system
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@publishing": path.resolve(__dirname, "./src/publishing"),
      "@management": path.resolve(__dirname, "./src/management"),
    },
  },
  // Use the publishing HTML file
  build: {
    outDir: "dist-publishing",
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, "publishing.html"),
      },
    },
  },
  // Explicitly set the entry point for development
  optimizeDeps: {
    entries: [
      path.resolve(__dirname, "publishing.html")
    ]
  },
  // Force the entry point to be src/publishing/index.tsx
  define: {
    'process.env.VITE_APP_TYPE': JSON.stringify('publishing'),
    'import.meta.env.VITE_APP_TYPE': JSON.stringify('publishing')
  },
  // 明确指定入口文件
  root: './',
  publicDir: 'public',
  base: '/'
}));
