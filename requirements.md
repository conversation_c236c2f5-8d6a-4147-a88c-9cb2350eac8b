# VisionOrb With AI Assistant for Video Comprehend

## 需求描述
你是一名专业的前后端应用开发工程师，同时精通音视频处理技术，请帮我开发一个基于视频内容的多媒体内容管理及发布系统，系统由前端+后端两个项目组成，本此开发内容只涉及前端项目，UI设计上需要体现AI大模型+人工智能的特点，前端项目名称为visionOrb-frontend。

## 前端系统设计
前端系统包含2部分，内容管理子系统和内容发布子系统，两个子系统共用一套账号体系，并有2个不同的入口。业务流程：在内容管理子系统中，可以上传视频素材进入素材库，后台会自动进行视频处理环节，处理完成后由人工提交审进入审核库，管理员审核通过后，进入到成品库并默认设置为已发布状态，成品库中已发布的素材最终在内容发布子系统中展示。

### 内容管理子系统
1. 用户登录
    - 支持用户名密码登录方式
2. 内容管理
    - 素材库
        - 素材列表页：展示已上传的素材列表
            - 支持上传视频文件
            - 支持多条件查询：包括名称、来源、类型、处理状态、创建时间等条件筛选
            - 来源分类：上传、收录、导入
            - 类型：视频、音频
            - 素材列表字段：包含名称、来源、类型、编目状态、处理状态、创建时间
            - 操作：包括编辑、提交审核、删除，点击某条素材的编辑按钮，跳转到素材详情页
        - 素材详情页
            - 视频播放器：可在线播放视频内容
            - 视频缩略图：默认第一张为封面图片，用户可自行选择其他图片或上传本地图片作为封面
            - 配音文稿：按照时间点+文字内容展示视频中音频内容的文字信息，类似歌词显示方式
            - 视频基本信息：展示视频基本信息，如时长、比例、分辨率、码率、编码格式、文件大小等
            - 内容摘要：显示视频的内容摘要信息，支持用户编辑并保存
            - 基本信息：显示视频基本信息，如时长、比例、分辨率、码率、编码格式、文件大小等
            - 人工编目：根据系统设置中的编目模板字段配置，显示对应的字段输入框并提供给用户输入
            - 特征信息：展示后台识别出来的特征结果，特征信息包含识别出的敏感人脸信息、敏感标志物及敏感词，并展示每个特征信息对应视频的时间点
            - 大模型问答：基于视频内容可以与大模型进行问答对话
            - 文书生成：点击文书生成按钮，可以调用后端接口生成文书

    - 审核库
        - 审核列表页：展示待审核的素材列表
            - 多条件查询：包括名称、来源、类型、提交人、审核状态、提交时间这些条件筛选
            - 审核列表：显示名称、来源、类型、提交人、审核状态、提交时间、审核时间、操作等字段
            - 点击"操作"按钮，跳转到审核详情页
        - 审核详情页
            - 视频播放器：可在线播放视频内容
            - 视频缩略图：显示封面图片信息
            - 配音文稿：按照时间点+文字内容展示视频中音频内容的文字信息，类似歌词显示方式
            - 视频基本信息：展示视频基本信息，如时长、比例、分辨率、码率、编码格式、文件大小等
            - 内容摘要：显示视频的内容摘要信息
            - 基本信息：显示视频基本信息，如时长、比例、分辨率、码率、编码格式、文件大小等
            - 人工编目：显示编目信息
            - 特征信息：展示后台识别出来的特征结果，特征信息包含识别出的敏感人脸信息、敏感标志物及敏感词，并展示每个特征信息对应视频的时间点
            - 大模型问答：基于视频内容可以与大模型进行问答对话
            - 文书生成：点击文书生成按钮，可以调用后端接口生成文书
            - 审核操作：审核通过/审核不通过，不通过时需要填写理由。审核通过的素材进入到发布库，不通过的素材退回到素材库
    - 发布库
        - 发布列表页：展示已审核通过的素材列表，并默认设置为已发布状态
            - 发布列表：显示名称、来源、类型、提交人、审核状态、提交时间、审核时间、审核人、发布状态、操作等字段
            - 点击操作按钮：可以弹窗提示将素材修改为未发布状态
    - 回收站
        - 从素材库中删除的素材，进入到回收站
        - 点击清空回收站按钮，素材彻底从数据库中删除
3. 特征库
    - 人物库
        - 新建人物库，输入名称和描述，支持创建多个人物库，并支持修改、删除操作
        - 点击进入某个人物库，支持上传1张或多张人脸照片，填写名称、别名、性别、年龄、国籍、身份证号、护照号、标签、描述等信息，并展示出来，支持修改、删除操作
    - 标记库
        - 新建图像库，输入名称和描述，支持创建多个人脸库，并支持修改、删除操作
        - 点击进入某个标记库，支持上传1张或多张标记图片，填写名称、描述、标签等信息，并展示出来，支持修改、删除操作
    - 敏感词库
        - 新建敏感词库，支持创建多个敏感词库，手动输入敏感词
4. 统计分析
    - 按时间段统计素材总量、不同来源的数量、待审核数量、已发布数量，并显示折线图
5. 日志管理
    - 操作日志：包含系统登录、系统编目、审核等关键性动作信息，并记录每项操作的具体时间、操作员和操作的内容。并提供检索功能
6. 系统设置
    - 转码模板
        - 模板管理：支持新建、编辑、删除模板
        - 设为默认：选中某个模板，可以设置为系统默认
        - 新建模板：填写模板名称、视频编码、视频帧率、视频码率、视频宽度、视频高度、GOP、音频编码、音频码率、封装格式、文件扩展名、扩展参数
    - 编目模板
        - 模板管理：支持新建、编辑、删除编目模板
        - 设为默认：选中某个模板，可以设置为系统默认
        - 编目项：可新建编目项，输入名称并选择类型，类型包含文本、下拉列表、选择框等常见表单项，并提供修改、删除功能
        - 编目模板：可新建编目模板，输入名称，并从编目项中挑选字段进行组合，并提供修改、删除功能
    - 角色管理：可以自定义创建多个系统角色
    - 权限管理：根据角色可以按照功能菜单分配不同的权限
    - 用户管理：管理员可创建和管理用户并分配角色，新建用户字段包括用户名称、部门、角色、手机、邮箱，支持新建用户、删除用户、重置密码、修改密码功能

### 内容发布子系统
1. 用户登录
    - 支持用户名密码登录方式
2. 首页
    - 内容搜索：输入关键词可检索视频内容，跳转到检索结果列表页面
    - 内容分类：按照视频分类标签分类展示视频内容
    - 内容展示：包含视频缩略图、名称、简介
3. 检索结果页
    - 检索结果：展示检索结果列表页面
4. 详情页
    - 视频播放：点击可以播放视频内容
    - 缩略图展示：可以展示视频的多张缩略图
    - 配音文稿：按照时间点+文字内容展示视频中音频内容的文字信息，类似歌词显示方式
    - 内容摘要：显示视频的内容摘要信息
    - 基本信息：显示视频基本信息，如时长、比例、分辨率、码率、编码格式、文件大小等
    - 编目信息：显示视频编目信息
    - 特征信息：特征信息包含识别出的敏感人脸信息、敏感标志物及敏感词，并展示每个特征信息对应视频的时间点，点击某条记录，视频播放器可跳转到对应的时间点
    - 大模型问答：基于视频内容可以与大模型进行问答对话

## 交互设计
1. 要求整体布局合理，满足易用性要求
2. 界面配色协调，体现智能和安全特点
3. 缩略图展示功能需要设计的更有科技感，可以参考类似电影屏幕那种照片墙的效果并增加特效以增强交互体验

## 技术栈要求
1. 开发语言：TypeScript
2. 代码结构清晰明了，核心部分需要增加注释
3. 模块划分清晰，并充分考虑可扩展性

## 补充说明
1. 系统所有的数据均通过API接口从后端服务获取
2. 在调试运行阶段，可以模拟假数据进行页面效果展示