
import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import {
  Video,
  FileVideo,
  Check,
  Upload,
  Trash2,
  Users,
  Tag,
  FileText,
  BarChart2,
  Settings,
  ChevronRight,
  Database,
  Shield,
  Cog
} from "lucide-react";
import { cn } from "@/lib/utils";

interface SidebarItemProps {
  icon: React.ReactNode;
  label: string;
  to: string;
  end?: boolean;
}

interface SidebarGroupProps {
  icon: React.ReactNode;
  label: string;
  children: React.ReactNode;
}

const SidebarItem = ({ icon, label, to, end = false }: SidebarItemProps) => (
  <NavLink
    to={to}
    end={end}
    className={({ isActive }) =>
      cn(
        "flex items-center space-x-2 px-4 py-2 rounded-md hover:bg-gray-800 transition-colors",
        isActive ? "bg-primary-purple text-white" : "text-gray-300"
      )
    }
  >
    <div className="w-5 h-5">{icon}</div>
    <span>{label}</span>
  </NavLink>
);

const SidebarGroup = ({ icon, label, children }: SidebarGroupProps) => {
  const [isOpen, setIsOpen] = useState(true);
  const location = useLocation();
  
  // Check if any child route is active to auto-expand the group
  const childRoutes = Array.isArray(children) 
    ? children.map((child: any) => child.props.to) 
    : [];
  
  const isActive = childRoutes.some(route => location.pathname.startsWith(route));
  
  return (
    <div>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "flex items-center justify-between w-full px-4 py-2 text-left rounded-md hover:bg-gray-800 transition-colors",
          isActive ? "text-primary-purple" : "text-gray-300"
        )}
      >
        <div className="flex items-center space-x-2">
          <div className="w-5 h-5">{icon}</div>
          <span>{label}</span>
        </div>
        <ChevronRight
          className={cn(
            "w-4 h-4 transition-transform",
            isOpen ? "transform rotate-90" : ""
          )}
        />
      </button>
      {isOpen && <div className="mt-1 ml-4 space-y-1">{children}</div>}
    </div>
  );
};

export const Sidebar = () => {
  return (
    <div className="w-64 h-screen flex-shrink-0 border-r border-gray-700 bg-dark-foreground overflow-y-auto">
      <div className="flex items-center justify-center h-16 border-b border-gray-700">
        <h1 className="text-xl font-bold text-primary-purple">
          VisionOrb
        </h1>
      </div>

      <nav className="mt-6 px-3 space-y-6">
        <SidebarGroup icon={<FileVideo className="w-5 h-5" />} label="内容管理">
          <SidebarItem icon={<Video className="w-5 h-5" />} label="素材库" to="/materials" />
          <SidebarItem icon={<Check className="w-5 h-5" />} label="审核库" to="/audits" />
          <SidebarItem icon={<Upload className="w-5 h-5" />} label="发布库" to="/published" />
          <SidebarItem icon={<Trash2 className="w-5 h-5" />} label="回收站" to="/trash" />
        </SidebarGroup>

        <SidebarGroup icon={<Tag className="w-5 h-5" />} label="特征库">
          <SidebarItem icon={<Users className="w-5 h-5" />} label="人物库" to="/features/people" />
          <SidebarItem icon={<Tag className="w-5 h-5" />} label="标记库" to="/features/markers" />
          <SidebarItem icon={<FileText className="w-5 h-5" />} label="敏感词库" to="/features/keywords" />
        </SidebarGroup>

        <div className="space-y-1">
          <SidebarItem icon={<BarChart2 className="w-5 h-5" />} label="统计分析" to="/analytics" />
          <SidebarItem icon={<FileText className="w-5 h-5" />} label="日志管理" to="/logs" />
        </div>

        <SidebarGroup icon={<Settings className="w-5 h-5" />} label="系统设置">
          <SidebarItem icon={<Database className="w-5 h-5" />} label="转码模板" to="/settings/transcode" />
          <SidebarItem icon={<FileText className="w-5 h-5" />} label="编目模板" to="/settings/catalog" />
          <SidebarItem icon={<Shield className="w-5 h-5" />} label="角色管理" to="/settings/roles" />
          <SidebarItem icon={<Cog className="w-5 h-5" />} label="权限管理" to="/settings/permissions" />
          <SidebarItem icon={<Users className="w-5 h-5" />} label="用户管理" to="/settings/users" />
        </SidebarGroup>
      </nav>
    </div>
  );
};
