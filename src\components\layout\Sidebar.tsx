
import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import {
  Video,
  FileVideo,
  Check,
  Upload,
  Trash2,
  Users,
  Tag,
  FileText,
  BarChart2,
  Settings,
  ChevronRight,
  Database,
  Shield,
  Cog,
  Sparkles
} from "lucide-react";
import { cn } from "@/lib/utils";

interface SidebarItemProps {
  icon: React.ReactNode;
  label: string;
  to: string;
  end?: boolean;
}

interface SidebarGroupProps {
  icon: React.ReactNode;
  label: string;
  children: React.ReactNode;
}

const SidebarItem = ({ icon, label, to, end = false }: SidebarItemProps) => (
  <NavLink
    to={to}
    end={end}
    className={({ isActive }) =>
      cn(
        "group flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ease-in-out relative overflow-hidden",
        "hover:bg-gradient-to-r hover:from-primary-purple/20 hover:to-primary-purple/10",
        "hover:shadow-lg hover:shadow-primary-purple/20 hover:scale-[1.02]",
        "before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/5 before:to-transparent",
        "before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700",
        isActive
          ? "bg-gradient-to-r from-primary-purple to-primary-dark text-white shadow-lg shadow-primary-purple/30"
          : "text-gray-300 hover:text-white"
      )
    }
  >
    <div className={cn(
      "w-5 h-5 transition-all duration-300",
      "group-hover:scale-110 group-hover:rotate-3"
    )}>
      {icon}
    </div>
    <span className="font-medium tracking-wide">{label}</span>
    {/* Active indicator */}
    <div className={cn(
      "absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-white rounded-l-full transition-all duration-300",
      isActive ? "opacity-100" : "opacity-0"
    )} />
  </NavLink>
);

const SidebarGroup = ({ icon, label, children }: SidebarGroupProps) => {
  const [isOpen, setIsOpen] = useState(true);
  const location = useLocation();

  // Check if any child route is active to auto-expand the group
  const childRoutes = Array.isArray(children)
    ? children.map((child: any) => child.props.to)
    : [];

  const isActive = childRoutes.some(route => location.pathname.startsWith(route));

  return (
    <div className="space-y-2">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "group flex items-center justify-between w-full px-4 py-3 text-left rounded-xl transition-all duration-300",
          "hover:bg-gradient-to-r hover:from-gray-800/50 hover:to-gray-700/30 hover:shadow-md",
          "border border-transparent hover:border-gray-600/30",
          isActive ? "text-primary-purple bg-primary-purple/10 border-primary-purple/20" : "text-gray-300 hover:text-white"
        )}
      >
        <div className="flex items-center space-x-3">
          <div className={cn(
            "w-5 h-5 transition-all duration-300",
            "group-hover:scale-110",
            isActive && "text-primary-purple"
          )}>
            {icon}
          </div>
          <span className="font-semibold tracking-wide text-sm uppercase">{label}</span>
        </div>
        <ChevronRight
          className={cn(
            "w-4 h-4 transition-all duration-300",
            "group-hover:scale-110",
            isOpen ? "rotate-90" : "",
            isActive && "text-primary-purple"
          )}
        />
      </button>
      <div className={cn(
        "overflow-hidden transition-all duration-300 ease-in-out",
        isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
      )}>
        <div className="ml-6 space-y-1 border-l border-gray-700/50 pl-4">
          {children}
        </div>
      </div>
    </div>
  );
};

export const Sidebar = () => {
  return (
    <div className="w-64 h-screen flex-shrink-0 bg-gradient-to-b from-gray-900 via-gray-900 to-gray-800 border-r border-gray-700/50 shadow-2xl overflow-y-auto sidebar-scrollbar">
      {/* Logo Section */}
      <div className="relative h-20 flex items-center justify-center border-b border-gray-700/50 bg-gradient-to-r from-primary-purple/10 to-primary-dark/10">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-50"></div>
        <div className="relative flex items-center space-x-3 group cursor-pointer">
          <div className="p-2 rounded-xl bg-gradient-to-br from-primary-purple to-primary-dark shadow-lg group-hover:shadow-primary-purple/30 transition-all duration-300 group-hover:scale-110">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-primary-purple to-primary-dark bg-clip-text text-transparent">
              VisionOrb
            </h1>
            <p className="text-xs text-gray-400 font-medium">内容管理系统</p>
          </div>
        </div>
      </div>

      <nav className="mt-8 px-4 space-y-8 pb-8">
        <SidebarGroup icon={<FileVideo className="w-5 h-5" />} label="内容管理">
          <SidebarItem icon={<Video className="w-5 h-5" />} label="素材库" to="/materials" />
          <SidebarItem icon={<Check className="w-5 h-5" />} label="审核库" to="/audits" />
          <SidebarItem icon={<Upload className="w-5 h-5" />} label="发布库" to="/published" />
          <SidebarItem icon={<Trash2 className="w-5 h-5" />} label="回收站" to="/trash" />
        </SidebarGroup>

        <SidebarGroup icon={<Tag className="w-5 h-5" />} label="特征库">
          <SidebarItem icon={<Users className="w-5 h-5" />} label="人物库" to="/features/people" />
          <SidebarItem icon={<Tag className="w-5 h-5" />} label="标记库" to="/features/markers" />
          <SidebarItem icon={<FileText className="w-5 h-5" />} label="敏感词库" to="/features/keywords" />
        </SidebarGroup>

        {/* Standalone Items */}
        <div className="space-y-3">
          <div className="px-4 py-2">
            <div className="h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent"></div>
          </div>
          <SidebarItem icon={<BarChart2 className="w-5 h-5" />} label="统计分析" to="/analytics" />
          <SidebarItem icon={<FileText className="w-5 h-5" />} label="日志管理" to="/logs" />
        </div>

        <SidebarGroup icon={<Settings className="w-5 h-5" />} label="系统设置">
          <SidebarItem icon={<Database className="w-5 h-5" />} label="转码模板" to="/settings/transcode" />
          <SidebarItem icon={<FileText className="w-5 h-5" />} label="编目模板" to="/settings/catalog" />
          <SidebarItem icon={<Shield className="w-5 h-5" />} label="角色管理" to="/settings/roles" />
          <SidebarItem icon={<Cog className="w-5 h-5" />} label="权限管理" to="/settings/permissions" />
          <SidebarItem icon={<Users className="w-5 h-5" />} label="用户管理" to="/settings/users" />
        </SidebarGroup>
      </nav>
    </div>
  );
};
