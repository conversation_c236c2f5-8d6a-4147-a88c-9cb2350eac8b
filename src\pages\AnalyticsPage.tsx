
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar } from "recharts";

// Mock data for analytics
const mockAnalyticsData = [
  { date: "2023-01", total: 120, upload: 70, collection: 30, import: 20, pending: 35, published: 45 },
  { date: "2023-02", total: 135, upload: 85, collection: 25, import: 25, pending: 40, published: 55 },
  { date: "2023-03", total: 145, upload: 90, collection: 35, import: 20, pending: 30, published: 65 },
  { date: "2023-04", total: 160, upload: 100, collection: 40, import: 20, pending: 35, published: 75 },
  { date: "2023-05", total: 180, upload: 110, collection: 45, import: 25, pending: 40, published: 85 },
  { date: "2023-06", total: 200, upload: 120, collection: 50, import: 30, pending: 45, published: 95 },
];

const mockSourceDistribution = [
  { name: "上传", value: 60 },
  { name: "收录", value: 25 },
  { name: "导入", value: 15 },
];

const mockProcessingStages = [
  { name: "待处理", value: 10 },
  { name: "处理中", value: 5 },
  { name: "处理完成", value: 85 },
];

const AnalyticsPage = () => {
  const [timeRange, setTimeRange] = useState("6months");
  
  // In a real app, we would filter data based on the selected time range
  const data = mockAnalyticsData;
  
  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">统计分析</h1>
        
        <div className="flex justify-end mb-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="3months">最近3个月</SelectItem>
              <SelectItem value="6months">最近6个月</SelectItem>
              <SelectItem value="1year">最近1年</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">素材总量</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{mockAnalyticsData[mockAnalyticsData.length - 1].total}</p>
              <p className="text-xs text-muted-foreground">较上月增长 {Math.round((mockAnalyticsData[mockAnalyticsData.length - 1].total / mockAnalyticsData[mockAnalyticsData.length - 2].total - 1) * 100)}%</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">上传素材</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{mockAnalyticsData[mockAnalyticsData.length - 1].upload}</p>
              <p className="text-xs text-muted-foreground">占总素材的 {Math.round((mockAnalyticsData[mockAnalyticsData.length - 1].upload / mockAnalyticsData[mockAnalyticsData.length - 1].total) * 100)}%</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">待审核数量</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{mockAnalyticsData[mockAnalyticsData.length - 1].pending}</p>
              <p className="text-xs text-muted-foreground">占总素材的 {Math.round((mockAnalyticsData[mockAnalyticsData.length - 1].pending / mockAnalyticsData[mockAnalyticsData.length - 1].total) * 100)}%</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">已发布数量</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-3xl font-bold">{mockAnalyticsData[mockAnalyticsData.length - 1].published}</p>
              <p className="text-xs text-muted-foreground">占总素材的 {Math.round((mockAnalyticsData[mockAnalyticsData.length - 1].published / mockAnalyticsData[mockAnalyticsData.length - 1].total) * 100)}%</p>
            </CardContent>
          </Card>
        </div>
        
        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>素材增长趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={data}
                    margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="date" 
                      label={{ value: '时间', position: 'insideBottomRight', offset: -10 }}
                    />
                    <YAxis 
                      label={{ value: '数量', angle: -90, position: 'insideLeft' }}
                    />
                    <Tooltip />
                    <Legend verticalAlign="top" height={36} />
                    <Line type="monotone" dataKey="total" name="素材总量" stroke="#8884d8" strokeWidth={2} activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="upload" name="上传素材" stroke="#82ca9d" />
                    <Line type="monotone" dataKey="collection" name="收录素材" stroke="#ffc658" />
                    <Line type="monotone" dataKey="import" name="导入素材" stroke="#ff7300" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>素材来源分布</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={mockSourceDistribution}
                      margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis label={{ value: '百分比 (%)', angle: -90, position: 'insideLeft' }} />
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Bar dataKey="value" fill="#8884d8" name="百分比" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>素材处理状态</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={mockProcessingStages}
                      margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis label={{ value: '百分比 (%)', angle: -90, position: 'insideLeft' }} />
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Bar dataKey="value" fill="#82ca9d" name="百分比" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default AnalyticsPage;
