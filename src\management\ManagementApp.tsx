import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/context/AuthContext";

// Import management system pages
import Login from "./pages/Login";
import NotFoundPage from "@/pages/NotFoundPage";
// 暂时使用原始路径，直到我们完成所有页面的迁移
import DashboardPage from "@/pages/DashboardPage";
import MaterialsPage from "@/pages/MaterialsPage";
import MaterialDetailPage from "@/pages/MaterialDetailPage";
import AuditPage from "@/pages/AuditPage";
import PublishedPage from "@/pages/PublishedPage";
import TrashPage from "@/pages/TrashPage";
import PeoplePage from "@/pages/features/PeoplePage";
import MarkersPage from "@/pages/features/MarkersPage";
import KeywordsPage from "@/pages/features/KeywordsPage";
import AnalyticsPage from "@/pages/AnalyticsPage";
import LogsPage from "@/pages/LogsPage";
import SettingsPage from "@/pages/SettingsPage";
import TranscodeSettingsPage from "@/pages/settings/TranscodeSettingsPage";
import CatalogSettingsPage from "@/pages/settings/CatalogSettingsPage";
import RolesSettingsPage from "@/pages/settings/RolesSettingsPage";
import PermissionsSettingsPage from "@/pages/settings/PermissionsSettingsPage";
import UsersSettingsPage from "@/pages/settings/UsersSettingsPage";

const queryClient = new QueryClient();

export const ManagementApp = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={<DashboardPage />} />
            <Route path="/materials" element={<MaterialsPage />} />
            <Route path="/materials/:id" element={<MaterialDetailPage />} />
            <Route path="/audits" element={<AuditPage />} />
            <Route path="/published" element={<PublishedPage />} />
            <Route path="/trash" element={<TrashPage />} />
            <Route path="/features/people" element={<PeoplePage />} />
            <Route path="/features/markers" element={<MarkersPage />} />
            <Route path="/features/keywords" element={<KeywordsPage />} />
            <Route path="/analytics" element={<AnalyticsPage />} />
            <Route path="/logs" element={<LogsPage />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="/settings/transcode" element={<TranscodeSettingsPage />} />
            <Route path="/settings/catalog" element={<CatalogSettingsPage />} />
            <Route path="/settings/roles" element={<RolesSettingsPage />} />
            <Route path="/settings/permissions" element={<PermissionsSettingsPage />} />
            <Route path="/settings/users" element={<UsersSettingsPage />} />
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);
