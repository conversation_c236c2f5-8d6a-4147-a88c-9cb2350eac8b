
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const NotFoundPage = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-background">
      <div className="text-center glass-morphism p-8 rounded-lg max-w-md">
        <h1 className="text-6xl font-bold text-primary-purple mb-6">404</h1>
        <h2 className="text-2xl font-semibold mb-4">页面未找到</h2>
        <p className="text-gray-400 mb-6">
          您访问的页面不存在或已被移除。
        </p>
        <div className="flex justify-center">
          <Button 
            className="bg-primary-purple hover:bg-primary-dark" 
            onClick={() => navigate("/")}
          >
            返回首页
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
