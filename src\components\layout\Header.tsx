
import { useAuth } from "@/context/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import { LogOut, User, Settings, Bell } from "lucide-react";
import { Button } from "@/components/ui/button";

export const Header = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  return (
    <header className="relative h-16 bg-gradient-to-r from-gray-900/95 via-gray-800/95 to-gray-900/95 backdrop-blur-xl border-b border-gray-700/50 shadow-lg">
      {/* Background overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-50"></div>

      <div className="relative flex items-center justify-between h-full px-6">
        {/* Left side - could add breadcrumbs or search later */}
        <div className="flex-1"></div>

        {/* Right side - notifications and user menu */}
        <div className="flex items-center space-x-4">
          {/* Notification Bell */}
          <Button
            variant="ghost"
            size="icon"
            className="relative h-10 w-10 rounded-full hover:bg-white/10 transition-all duration-300 hover:scale-110"
          >
            <Bell className="h-5 w-5 text-gray-300 hover:text-white transition-colors" />
            <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full animate-pulse"></span>
          </Button>

          {/* User Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger className="focus:outline-none">
              <div className="flex items-center space-x-3 px-3 py-2 rounded-xl hover:bg-white/10 transition-all duration-300 group">
                <Avatar className="h-9 w-9 border-2 border-primary-purple/50 group-hover:border-primary-purple transition-all duration-300 group-hover:scale-110">
                  <AvatarImage src={user?.avatar} />
                  <AvatarFallback className="bg-gradient-to-br from-primary-purple to-primary-dark text-white font-semibold">
                    {user?.username?.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden md:block text-left">
                  <p className="text-sm font-medium text-white group-hover:text-primary-purple transition-colors">
                    {user?.username}
                  </p>
                  <p className="text-xs text-gray-400">管理员</p>
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-64 bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 shadow-2xl">
              <DropdownMenuLabel className="p-4">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12 border-2 border-primary-purple/50">
                    <AvatarImage src={user?.avatar} />
                    <AvatarFallback className="bg-gradient-to-br from-primary-purple to-primary-dark text-white font-semibold">
                      {user?.username?.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm font-semibold text-white">{user?.username}</p>
                    <p className="text-xs text-gray-400">{user?.email || "<EMAIL>"}</p>
                    <p className="text-xs text-primary-purple font-medium">系统管理员</p>
                  </div>
                </div>
              </DropdownMenuLabel>

              <DropdownMenuSeparator className="bg-gray-700/50" />

              <DropdownMenuItem className="p-3 hover:bg-white/10 transition-colors cursor-pointer group">
                <User className="mr-3 h-4 w-4 text-gray-400 group-hover:text-primary-purple transition-colors" />
                <span className="text-gray-300 group-hover:text-white">个人资料</span>
              </DropdownMenuItem>

              <DropdownMenuItem className="p-3 hover:bg-white/10 transition-colors cursor-pointer group">
                <Settings className="mr-3 h-4 w-4 text-gray-400 group-hover:text-primary-purple transition-colors" />
                <span className="text-gray-300 group-hover:text-white">系统设置</span>
              </DropdownMenuItem>

              <DropdownMenuSeparator className="bg-gray-700/50" />

              <DropdownMenuItem
                onClick={handleLogout}
                className="p-3 hover:bg-red-500/20 transition-colors cursor-pointer group text-red-400 hover:text-red-300"
              >
                <LogOut className="mr-3 h-4 w-4 group-hover:scale-110 transition-transform" />
                <span className="font-medium">退出登录</span>
              </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};
