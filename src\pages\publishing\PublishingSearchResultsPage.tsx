import { useEffect, useState } from "react";
import { PublishingLayout } from "@/components/layout/PublishingLayout";
import { Card, CardContent } from "@/components/ui/card";
import { useNavigate, useLocation } from "react-router-dom";
import { AuditStatus, MediaType, PublishStatus, VideoMaterial, VideoSource } from "@/types";

// Mock data for demonstration
const mockPublishedItems: VideoMaterial[] = [
  {
    id: "pub-1",
    name: "公司年会宣传视频",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-12",
    submittedAt: "2023-05-13",
    auditedAt: "2023-05-14",
    submittedBy: "张三",
    auditedBy: "李四",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段公司年会的宣传视频，展示了公司的文化和员工风采。",
  },
  {
    id: "pub-2",
    name: "产品演示视频",
    source: VideoSource.COLLECTION,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-10",
    submittedAt: "2023-05-11",
    auditedAt: "2023-05-12",
    submittedBy: "王五",
    auditedBy: "赵六",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段产品演示视频，详细介绍了产品的功能和使用方法。",
  },
  {
    id: "pub-3",
    name: "技术培训视频",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-08",
    submittedAt: "2023-05-09",
    auditedAt: "2023-05-10",
    submittedBy: "李四",
    auditedBy: "张三",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段技术培训视频，包含了详细的技术讲解和实操演示。",
  },
  {
    id: "pub-4",
    name: "市场分析报告",
    source: VideoSource.IMPORT,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-06",
    submittedAt: "2023-05-07",
    auditedAt: "2023-05-08",
    submittedBy: "赵六",
    auditedBy: "王五",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段市场分析报告视频，分析了当前市场趋势和未来发展方向。",
  },
];

const PublishingSearchResultsPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<VideoMaterial[]>([]);

  useEffect(() => {
    const query = new URLSearchParams(location.search).get("q") || "";
    setSearchQuery(query);
    
    // Filter videos based on search query
    const results = mockPublishedItems.filter(
      video => 
        video.name.toLowerCase().includes(query.toLowerCase()) || 
        (video.description && video.description.toLowerCase().includes(query.toLowerCase()))
    );
    
    setSearchResults(results);
  }, [location.search]);

  const handleVideoClick = (id: string) => {
    navigate(`/detail/${id}`);
  };

  return (
    <PublishingLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">搜索结果: "{searchQuery}"</h1>
          <p className="text-gray-400">找到 {searchResults.length} 个结果</p>
        </div>

        {searchResults.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {searchResults.map((video) => (
              <Card 
                key={video.id} 
                className="overflow-hidden cursor-pointer hover:shadow-lg transition-shadow duration-300 bg-dark-foreground border-gray-700"
                onClick={() => handleVideoClick(video.id)}
              >
                <div className="aspect-video relative overflow-hidden group">
                  <img 
                    src={video.thumbnails[0] || "/placeholder.svg"} 
                    alt={video.name} 
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                    <div className="p-4 w-full">
                      <span className="text-xs bg-primary-purple px-2 py-1 rounded-full">
                        {video.type}
                      </span>
                    </div>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-2 line-clamp-1">{video.name}</h3>
                  <p className="text-sm text-gray-400 line-clamp-2">{video.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-400 mb-4">没有找到与 "{searchQuery}" 相关的内容</p>
            <button 
              className="text-primary-purple hover:underline"
              onClick={() => navigate("/")}
            >
              返回首页
            </button>
          </div>
        )}
      </div>
    </PublishingLayout>
  );
};

export default PublishingSearchResultsPage;
