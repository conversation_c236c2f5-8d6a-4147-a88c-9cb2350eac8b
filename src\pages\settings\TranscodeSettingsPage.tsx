
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, Pencil, Trash } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";

// Mock data for transcode templates
interface TranscodeTemplate {
  id: string;
  name: string;
  videoCodec: string;
  videoFrameRate: string;
  videoBitrate: string;
  videoWidth: string;
  videoHeight: string;
  gop: string;
  audioCodec: string;
  audioBitrate: string;
  format: string;
  extension: string;
  isDefault: boolean;
}

const mockTranscodeTemplates: TranscodeTemplate[] = [
  {
    id: "tt-1",
    name: "高清格式",
    videoCodec: "H.264",
    videoFrameRate: "30fps",
    videoBitrate: "6Mbps",
    videoWidth: "1920",
    videoHeight: "1080",
    gop: "60",
    audioCodec: "AAC",
    audioBitrate: "192Kbps",
    format: "MP4",
    extension: ".mp4",
    isDefault: true,
  },
  {
    id: "tt-2",
    name: "标清格式",
    videoCodec: "H.264",
    videoFrameRate: "25fps",
    videoBitrate: "2Mbps",
    videoWidth: "1280",
    videoHeight: "720",
    gop: "50",
    audioCodec: "AAC",
    audioBitrate: "128Kbps",
    format: "MP4",
    extension: ".mp4",
    isDefault: false,
  },
  {
    id: "tt-3",
    name: "流畅格式",
    videoCodec: "H.264",
    videoFrameRate: "24fps",
    videoBitrate: "1Mbps",
    videoWidth: "852",
    videoHeight: "480",
    gop: "48",
    audioCodec: "AAC",
    audioBitrate: "96Kbps",
    format: "MP4",
    extension: ".mp4",
    isDefault: false,
  },
];

const TranscodeSettingsPage = () => {
  const [transcodeTemplates, setTranscodeTemplates] = useState<TranscodeTemplate[]>(mockTranscodeTemplates);

  // Set a template as default
  const setDefaultTemplate = (id: string) => {
    setTranscodeTemplates(
      transcodeTemplates.map(template => ({
        ...template,
        isDefault: template.id === id
      }))
    );
    toast({
      title: "默认模板已更新",
      description: "转码模板设置成功",
    });
  };

  // Mock handlers for various actions
  const handleCreateTemplate = () => {
    toast({
      title: "创建模板",
      description: "此处将打开转码模板创建表单",
    });
  };

  const handleEditTemplate = (id: string) => {
    toast({
      title: "编辑模板",
      description: `此处将打开ID为${id}的转码模板编辑表单`,
    });
  };

  const handleDeleteTemplate = (id: string) => {
    toast({
      title: "删除模板",
      description: `此处将确认删除ID为${id}的转码模板`,
      variant: "destructive",
    });
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">转码模板设置</h1>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>转码模板设置</CardTitle>
              <CardDescription>管理视频转码的默认输出格式</CardDescription>
            </div>
            <Button onClick={() => handleCreateTemplate()} className="flex items-center">
              <PlusCircle className="mr-2 h-4 w-4" />
              新建模板
            </Button>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>模板名称</TableHead>
                  <TableHead>视频编码</TableHead>
                  <TableHead>分辨率</TableHead>
                  <TableHead>帧率</TableHead>
                  <TableHead>音频编码</TableHead>
                  <TableHead>封装格式</TableHead>
                  <TableHead>默认</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transcodeTemplates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">{template.name}</TableCell>
                    <TableCell>{template.videoCodec}</TableCell>
                    <TableCell>{template.videoWidth}x{template.videoHeight}</TableCell>
                    <TableCell>{template.videoFrameRate}</TableCell>
                    <TableCell>{template.audioCodec}</TableCell>
                    <TableCell>{template.format}</TableCell>
                    <TableCell>
                      {template.isDefault ? (
                        <Badge className="bg-green-500">默认</Badge>
                      ) : (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => setDefaultTemplate(template.id)}
                        >
                          设为默认
                        </Button>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="mr-1"
                        onClick={() => handleEditTemplate(template.id)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        disabled={template.isDefault}
                        onClick={() => handleDeleteTemplate(template.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default TranscodeSettingsPage;
