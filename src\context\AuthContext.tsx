
import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { User, AuthState } from "@/types";

interface AuthContextType extends AuthState {
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  isPublishingSystem?: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
  isPublishingSystem?: boolean;
}

export const AuthProvider = ({ children, isPublishingSystem: propIsPublishing = false }: AuthProviderProps) => {
  // 检查全局变量和属性，确定是否是内容发布系统
  const windowIsPublishing = typeof window !== 'undefined' && (window as any).IS_PUBLISHING_SYSTEM === true;
  const isPublishingSystem = windowIsPublishing || propIsPublishing;

  console.log('=== AuthProvider ===');
  console.log('AuthProvider: propIsPublishing =', propIsPublishing);
  console.log('AuthProvider: windowIsPublishing =', windowIsPublishing);
  console.log('AuthProvider: isPublishingSystem =', isPublishingSystem);

  // 根据系统类型使用不同的存储键
  const tokenKey = isPublishingSystem ? "publishing_token" : "token";
  const userKey = isPublishingSystem ? "publishing_user" : "user";

  console.log('AuthProvider: tokenKey =', tokenKey);
  console.log('AuthProvider: userKey =', userKey);

  const [authState, setAuthState] = useState<AuthState>(() => {
    const storedToken = localStorage.getItem(tokenKey);
    const storedUser = localStorage.getItem(userKey);

    console.log('AuthProvider: storedToken =', storedToken);
    console.log('AuthProvider: storedUser =', storedUser);

    const initialState = {
      user: storedUser ? JSON.parse(storedUser) : null,
      token: storedToken || null,
      isLoading: false,
      error: null,
    };

    console.log('AuthProvider: initialState =', initialState);
    console.log('========================');

    return initialState;
  });

  useEffect(() => {
    console.log('AuthProvider useEffect: authState changed', authState);

    if (authState.token) {
      console.log(`AuthProvider: Setting ${tokenKey} in localStorage`);
      localStorage.setItem(tokenKey, authState.token);

      if (authState.user) {
        console.log(`AuthProvider: Setting ${userKey} in localStorage`, authState.user);
        localStorage.setItem(userKey, JSON.stringify(authState.user));
      }
    } else {
      console.log(`AuthProvider: Removing ${tokenKey} and ${userKey} from localStorage`);
      localStorage.removeItem(tokenKey);
      localStorage.removeItem(userKey);
    }

    // 打印当前 localStorage 的状态
    console.log('Current localStorage state:');
    console.log('token =', localStorage.getItem('token'));
    console.log('user =', localStorage.getItem('user'));
    console.log('publishing_token =', localStorage.getItem('publishing_token'));
    console.log('publishing_user =', localStorage.getItem('publishing_user'));
  }, [authState.token, authState.user, tokenKey, userKey]);

  // Mock login function - in a real app, would make an API call
  const login = async (username: string, password: string) => {
    try {
      console.log('=== login function ===');
      console.log('login: username =', username);
      console.log('login: password =', password);
      console.log('login: isPublishingSystem =', isPublishingSystem);
      console.log('login: tokenKey =', tokenKey);
      console.log('login: userKey =', userKey);

      setAuthState(prev => {
        console.log('login: Setting isLoading to true');
        return { ...prev, isLoading: true, error: null };
      });

      // Simulate API call delay
      console.log('login: Simulating API call delay');
      await new Promise(r => setTimeout(r, 1000));

      // Mock validation
      if (username === "admin" && password === "password") {
        console.log('login: Validation successful');

        const mockUser: User = {
          id: "1",
          username: "admin",
          role: "administrator",
          email: "<EMAIL>",
          department: isPublishingSystem ? "发布部门" : "管理部门",
          avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGF2YXRhcnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60",
        };

        console.log('login: Created mockUser', mockUser);

        const newToken = isPublishingSystem ? "publishing-token-12345" : "management-token-12345";
        console.log('login: Created newToken', newToken);

        console.log('login: Setting new authState');
        setAuthState({
          user: mockUser,
          token: newToken,
          isLoading: false,
          error: null,
        });

        console.log('login: New authState set');
      } else {
        console.log('login: Validation failed');
        throw new Error("用户名或密码错误");
      }
    } catch (error) {
      console.error('login: Error occurred', error);

      setAuthState(prev => {
        console.log('login: Setting error state');
        return {
          ...prev,
          isLoading: false,
          error: error instanceof Error ? error.message : "登录时发生未知错误",
          token: null,
          user: null,
        };
      });
    } finally {
      console.log('=== login function end ===');
    }
  };

  const logout = () => {
    console.log('=== logout function ===');
    console.log('logout: isPublishingSystem =', isPublishingSystem);
    console.log('logout: tokenKey =', tokenKey);
    console.log('logout: userKey =', userKey);

    console.log('logout: Setting authState to null');
    setAuthState({
      user: null,
      token: null,
      isLoading: false,
      error: null,
    });

    console.log('=== logout function end ===');
  };

  const isAuthenticated = !!authState.token;
  console.log('AuthProvider: isAuthenticated =', isAuthenticated);

  const value = {
    ...authState,
    login,
    logout,
    isAuthenticated,
    isPublishingSystem,
  };

  console.log('AuthProvider: value =', value);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
