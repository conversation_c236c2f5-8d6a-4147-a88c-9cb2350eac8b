
import { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

// This is a simple redirect page that checks authentication and redirects appropriately
const Index = () => {
  const { isAuthenticated } = useAuth();
  
  useEffect(() => {
    // Log that the index page is loading
    console.log('Index page loaded, checking authentication...');
  }, []);

  // Redirect based on authentication status
  if (isAuthenticated) {
    console.log('User authenticated, redirecting to dashboard');
    return <Navigate to="/" replace />;
  } else {
    console.log('User not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }
};

export default Index;
