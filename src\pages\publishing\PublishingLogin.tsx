import { useState } from "react";
import { useAuth } from "@/context/AuthContext";
import { useNavigate, Navigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";

const PublishingLogin = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const { login, isLoading, error, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!username || !password) {
      toast({
        title: "错误",
        description: "请输入用户名和密码",
        variant: "destructive",
      });
      return;
    }

    try {
      await login(username, password);
      toast({
        title: "登录成功",
        description: "欢迎回来！",
      });
      navigate("/");
    } catch (err) {
      console.error(err);
    }
  };

  // 使用 useAuth 钩子获取 isPublishingSystem 属性
  const { isPublishingSystem } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-dark-background bg-gradient-to-br from-dark-foreground to-dark-background p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary-purple">VisionOrb</h1>
          <p className="text-gray-400 mt-2">内容发布系统</p>
        </div>

        <div className="glass-morphism rounded-lg p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                placeholder="请输入用户名"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                type="password"
                placeholder="请输入密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            {error && (
              <div className="text-red-500 text-sm py-2">{error}</div>
            )}

            <Button type="submit" className="w-full bg-primary-purple hover:bg-primary-dark" disabled={isLoading}>
              {isLoading ? "登录中..." : "登录"}
            </Button>

            <div className="text-xs text-gray-400 text-center mt-4">
              提示：使用用户名 admin 和密码 password 登录系统
            </div>
          </form>
        </div>

        <div className="text-center mt-6 text-xs text-gray-500">
          © 2025 VisionOrb. 保留所有权利。
        </div>
      </div>
    </div>
  );
};

export default PublishingLogin;
