
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Eye, Edit, Trash } from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

// Mock data structure for marker libraries
interface MarkerLibrary {
  id: string;
  name: string;
  description: string;
  markerCount: number;
  createdAt: string;
}

// Mock data
const mockMarkerLibraries: MarkerLibrary[] = [
  {
    id: "ml-1",
    name: "违禁标志物",
    description: "包含各类管控标志物的图像库",
    markerCount: 78,
    createdAt: "2023-01-22",
  },
  {
    id: "ml-2",
    name: "军事设施",
    description: "包含军事设施、军用装备等标志物的图像库",
    markerCount: 103,
    createdAt: "2023-02-18",
  },
  {
    id: "ml-3",
    name: "政治敏感",
    description: "政治敏感相关标志物图像库",
    markerCount: 57,
    createdAt: "2023-03-05",
  },
];

const MarkersPage = () => {
  const [libraries, setLibraries] = useState<MarkerLibrary[]>(mockMarkerLibraries);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [newLibraryName, setNewLibraryName] = useState("");
  const [newLibraryDescription, setNewLibraryDescription] = useState("");
  const [selectedLibrary, setSelectedLibrary] = useState<MarkerLibrary | null>(null);

  // Filter libraries based on search query
  const filteredLibraries = libraries.filter(library =>
    library.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    library.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddLibrary = () => {
    if (!newLibraryName.trim()) {
      toast({
        title: "错误",
        description: "标记库名称不能为空",
        variant: "destructive",
      });
      return;
    }

    const newLibrary: MarkerLibrary = {
      id: `ml-${Date.now()}`,
      name: newLibraryName,
      description: newLibraryDescription,
      markerCount: 0,
      createdAt: new Date().toISOString().split('T')[0],
    };

    setLibraries([...libraries, newLibrary]);
    setNewLibraryName("");
    setNewLibraryDescription("");
    setIsAddDialogOpen(false);
    
    toast({
      title: "创建成功",
      description: `标记库 "${newLibraryName}" 已成功创建`,
    });
  };

  const handleEditLibrary = () => {
    if (!selectedLibrary) return;
    
    if (!newLibraryName.trim()) {
      toast({
        title: "错误",
        description: "标记库名称不能为空",
        variant: "destructive",
      });
      return;
    }

    const updatedLibraries = libraries.map(lib => 
      lib.id === selectedLibrary.id 
        ? { ...lib, name: newLibraryName, description: newLibraryDescription } 
        : lib
    );

    setLibraries(updatedLibraries);
    setIsEditDialogOpen(false);
    
    toast({
      title: "修改成功",
      description: `标记库 "${newLibraryName}" 已更新`,
    });
  };

  const handleDeleteLibrary = () => {
    if (!selectedLibrary) return;

    const updatedLibraries = libraries.filter(lib => lib.id !== selectedLibrary.id);
    setLibraries(updatedLibraries);
    setIsDeleteDialogOpen(false);
    
    toast({
      title: "删除成功",
      description: `标记库 "${selectedLibrary.name}" 已删除`,
      variant: "destructive",
    });
  };

  const openEditDialog = (library: MarkerLibrary) => {
    setSelectedLibrary(library);
    setNewLibraryName(library.name);
    setNewLibraryDescription(library.description);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (library: MarkerLibrary) => {
    setSelectedLibrary(library);
    setIsDeleteDialogOpen(true);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">标记库</h1>
          <Button 
            className="flex items-center gap-2"
            onClick={() => {
              setNewLibraryName("");
              setNewLibraryDescription("");
              setIsAddDialogOpen(true);
            }}
          >
            <PlusCircle className="w-4 h-4" />
            新建标记库
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>标记库列表</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex w-full max-w-sm">
                <Input
                  placeholder="搜索标记库..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredLibraries.length > 0 ? (
                  filteredLibraries.map((library) => (
                    <Card key={library.id} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-lg">{library.name}</CardTitle>
                          <div className="flex gap-1">
                            <Button variant="ghost" size="icon" onClick={() => openEditDialog(library)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" onClick={() => openDeleteDialog(library)}>
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground text-sm line-clamp-2 mb-2">{library.description}</p>
                        <div className="flex justify-between items-center text-sm">
                          <span>包含 {library.markerCount} 个标记</span>
                          <span className="text-muted-foreground">创建于 {library.createdAt}</span>
                        </div>
                        <Button variant="outline" className="w-full mt-4" onClick={() => alert('此处会跳转到标记库详情页面')}>
                          <Eye className="h-4 w-4 mr-2" /> 查看
                        </Button>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="col-span-full text-center py-10 text-muted-foreground">
                    暂无标记库，请点击右上角"新建标记库"按钮创建
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Add Library Dialog */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>新建标记库</DialogTitle>
              <DialogDescription>
                创建一个新的标记库，用于管理标记图像识别信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">标记库名称</Label>
                <Input
                  id="name"
                  placeholder="输入标记库名称"
                  value={newLibraryName}
                  onChange={(e) => setNewLibraryName(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  placeholder="输入标记库描述..."
                  value={newLibraryDescription}
                  onChange={(e) => setNewLibraryDescription(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>取消</Button>
              <Button onClick={handleAddLibrary}>创建</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Library Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>编辑标记库</DialogTitle>
              <DialogDescription>
                修改标记库的名称和描述信息
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">标记库名称</Label>
                <Input
                  id="edit-name"
                  placeholder="输入标记库名称"
                  value={newLibraryName}
                  onChange={(e) => setNewLibraryName(e.target.value)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">描述</Label>
                <Textarea
                  id="edit-description"
                  placeholder="输入标记库描述..."
                  value={newLibraryDescription}
                  onChange={(e) => setNewLibraryDescription(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>取消</Button>
              <Button onClick={handleEditLibrary}>保存</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Library Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>删除标记库</DialogTitle>
              <DialogDescription>
                确定要删除 "{selectedLibrary?.name}" 标记库吗？此操作不可撤销。
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>取消</Button>
              <Button variant="destructive" onClick={handleDeleteLibrary}>确认删除</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  );
};

export default MarkersPage;
