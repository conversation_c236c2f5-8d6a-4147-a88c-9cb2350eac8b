import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight, Clock, Play } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { AuditStatus, MediaType, PublishStatus, VideoMaterial, VideoSource } from "@/types";
import { PublishingLayout } from "../components/layout/PublishingLayout";
import { useAuth } from "@/context/AuthContext";

// 扩展 VideoMaterial 类型，添加 category 和 views 属性
interface EnhancedVideoMaterial extends VideoMaterial {
  category?: string;
  views?: number;
  duration?: string;
}

// Mock data for demonstration
const mockPublishedItems: EnhancedVideoMaterial[] = [
  {
    id: "pub-1",
    name: "公司年会宣传视频",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-12",
    submittedAt: "2023-05-13",
    auditedAt: "2023-05-14",
    submittedBy: "张三",
    auditedBy: "李四",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段公司年会的宣传视频，展示了公司的文化和员工风采。",
    duration: "15:30",
    views: 1250,
    category: "cat-1"
  },
  {
    id: "pub-2",
    name: "产品演示视频",
    source: VideoSource.COLLECTION,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-10",
    submittedAt: "2023-05-11",
    auditedAt: "2023-05-12",
    submittedBy: "王五",
    auditedBy: "赵六",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段产品演示视频，详细介绍了产品的功能和使用方法。",
    duration: "10:15",
    views: 3450,
    category: "cat-2"
  },
  {
    id: "pub-3",
    name: "技术培训视频",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-08",
    submittedAt: "2023-05-09",
    auditedAt: "2023-05-10",
    submittedBy: "李四",
    auditedBy: "张三",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段技术培训视频，包含了详细的技术讲解和实操演示。",
    duration: "45:20",
    views: 2180,
    category: "cat-3"
  },
  {
    id: "pub-4",
    name: "市场分析报告",
    source: VideoSource.IMPORT,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-06",
    submittedAt: "2023-05-07",
    auditedAt: "2023-05-08",
    submittedBy: "赵六",
    auditedBy: "王五",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段市场分析报告视频，分析了当前市场趋势和未来发展方向。",
    duration: "20:45",
    views: 1870,
    category: "cat-4"
  },
  {
    id: "pub-5",
    name: "季度会议记录",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-04",
    submittedAt: "2023-05-05",
    auditedAt: "2023-05-06",
    submittedBy: "张三",
    auditedBy: "李四",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段季度会议的记录视频，包含了公司季度业绩和未来规划。",
    duration: "55:10",
    views: 980,
    category: "cat-5"
  },
  {
    id: "pub-6",
    name: "企业文化宣传片",
    source: VideoSource.COLLECTION,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-05-02",
    submittedAt: "2023-05-03",
    auditedAt: "2023-05-04",
    submittedBy: "王五",
    auditedBy: "赵六",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段企业文化宣传片，展示了公司的核心价值观和企业精神。",
    duration: "12:30",
    views: 2560,
    category: "cat-1"
  },
  {
    id: "pub-7",
    name: "新产品发布会",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-04-30",
    submittedAt: "2023-05-01",
    auditedAt: "2023-05-02",
    submittedBy: "李四",
    auditedBy: "张三",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段新产品发布会的视频，详细介绍了新产品的特点和优势。",
    duration: "35:45",
    views: 4120,
    category: "cat-2"
  },
  {
    id: "pub-8",
    name: "编程技术分享",
    source: VideoSource.IMPORT,
    type: MediaType.VIDEO,
    auditStatus: AuditStatus.APPROVED,
    publishStatus: PublishStatus.PUBLISHED,
    createdAt: "2023-04-28",
    submittedAt: "2023-04-29",
    auditedAt: "2023-04-30",
    submittedBy: "赵六",
    auditedBy: "王五",
    thumbnails: ["/placeholder.svg"],
    description: "这是一段编程技术分享视频，介绍了最新的编程技术和实践经验。",
    duration: "28:15",
    views: 3250,
    category: "cat-3"
  },
];

// 特色视频（轮播图使用）
const featuredVideos = [
  {
    id: "featured-1",
    name: "2023年度科技创新峰会",
    thumbnails: ["/placeholder.svg"],
    description: "汇聚全球顶尖科技专家，探讨前沿技术发展趋势和创新应用。",
    duration: "120:00",
    views: 8750
  },
  {
    id: "featured-2",
    name: "人工智能在企业中的应用",
    thumbnails: ["/placeholder.svg"],
    description: "深入剖析AI技术如何改变企业运营模式，提升效率与创新能力。",
    duration: "45:30",
    views: 6320
  },
  {
    id: "featured-3",
    name: "数字化转型成功案例分析",
    thumbnails: ["/placeholder.svg"],
    description: "通过真实案例，展示企业如何成功实现数字化转型，应对市场挑战。",
    duration: "60:15",
    views: 5480
  }
];

// Mock categories for demonstration
const categories = [
  { id: "cat-1", name: "企业宣传", icon: "🏢" },
  { id: "cat-2", name: "产品介绍", icon: "📱" },
  { id: "cat-3", name: "技术培训", icon: "💻" },
  { id: "cat-4", name: "市场分析", icon: "📊" },
  { id: "cat-5", name: "会议记录", icon: "📝" },
];

const PublishingHomePage = () => {
  console.log('=== PublishingHomePage.tsx ===');
  console.log('Rendering PublishingHomePage component');

  const [activeCategory, setActiveCategory] = useState<string>("all");
  const [currentSlide, setCurrentSlide] = useState(0);
  const navigate = useNavigate();
  const { isAuthenticated, isPublishingSystem } = useAuth();

  console.log('PublishingHomePage: isAuthenticated =', isAuthenticated);
  console.log('PublishingHomePage: isPublishingSystem =', isPublishingSystem);
  console.log('========================');

  // 设置全局变量，标识这是内容发布系统
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).IS_PUBLISHING_SYSTEM = true;
      console.log('PublishingHomePage useEffect: Setting window.IS_PUBLISHING_SYSTEM =', (window as any).IS_PUBLISHING_SYSTEM);
    }
  }, []);

  // 自动轮播
  useEffect(() => {
    console.log('PublishingHomePage: Setting up carousel interval');
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % featuredVideos.length);
    }, 5000);
    return () => {
      console.log('PublishingHomePage: Clearing carousel interval');
      clearInterval(interval);
    };
  }, []);

  // 按分类筛选视频
  const getVideosByCategory = (categoryId: string) => {
    return categoryId === "all"
      ? mockPublishedItems
      : mockPublishedItems.filter(video => video.category === categoryId);
  };

  const filteredVideos = getVideosByCategory(activeCategory);

  const handleVideoClick = (id: string) => {
    navigate(`/detail/${id}`);
  };

  // 格式化观看次数
  const formatViews = (views: number) => {
    if (views >= 10000) {
      return `${(views / 10000).toFixed(1)}万次观看`;
    }
    return `${views}次观看`;
  };

  return (
    <PublishingLayout>
      {/* 顶部标题 */}
      <div className="w-full mb-6 text-center">
        <h1 className="text-2xl font-bold text-primary-purple">内容发布系统</h1>
        <p className="text-gray-400">欢迎使用 VisionOrb 内容发布系统</p>
      </div>

      <div className="space-y-10">
        {/* 特色视频轮播 */}
        <div className="relative aspect-[21/9] rounded-xl overflow-hidden">
          {/* 轮播图背景 */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary-purple/30 to-gray-900/80"></div>

          {/* 轮播图内容 */}
          <div className="relative h-full">
            {featuredVideos.map((video, index) => (
              <div
                key={video.id}
                className={`absolute inset-0 transition-opacity duration-1000 ${
                  index === currentSlide ? "opacity-100" : "opacity-0 pointer-events-none"
                }`}
              >
                <img
                  src={video.thumbnails[0]}
                  alt={video.name}
                  className="absolute inset-0 w-full h-full object-cover opacity-60"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div>

                <div className="absolute bottom-0 left-0 p-6 md:p-10 w-full md:w-2/3 space-y-4">
                  <div className="flex items-center space-x-2 text-primary-purple">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">{video.duration}</span>
                    <span className="text-sm">•</span>
                    <span className="text-sm">{formatViews(video.views)}</span>
                  </div>
                  <h2 className="text-2xl md:text-4xl font-bold text-white">{video.name}</h2>
                  <p className="text-gray-300 text-sm md:text-base line-clamp-2 md:line-clamp-3">{video.description}</p>
                  <Button
                    className="bg-primary-purple hover:bg-primary-dark"
                    onClick={() => handleVideoClick(video.id)}
                  >
                    <Play className="h-4 w-4 mr-2" /> 立即观看
                  </Button>
                </div>
              </div>
            ))}

            {/* 轮播指示器 */}
            <div className="absolute bottom-4 right-4 flex space-x-2">
              {featuredVideos.map((_, index) => (
                <button
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all ${
                    index === currentSlide ? "bg-primary-purple w-6" : "bg-gray-400"
                  }`}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </div>
          </div>
        </div>

        {/* 分类导航 */}
        <div className="flex items-center justify-center space-x-2 overflow-x-auto py-2">
          <Button
            variant={activeCategory === "all" ? "default" : "outline"}
            className={activeCategory === "all" ? "bg-primary-purple hover:bg-primary-dark" : ""}
            onClick={() => setActiveCategory("all")}
          >
            全部内容
          </Button>
          {categories.map(category => (
            <Button
              key={category.id}
              variant={activeCategory === category.id ? "default" : "outline"}
              className={activeCategory === category.id ? "bg-primary-purple hover:bg-primary-dark" : ""}
              onClick={() => setActiveCategory(category.id)}
            >
              <span className="mr-2">{category.icon}</span>
              {category.name}
            </Button>
          ))}
        </div>

        {/* 视频列表 */}
        <div className="space-y-10">
          {activeCategory === "all" ? (
            // 按分类分组显示
            categories.map(category => {
              const categoryVideos = mockPublishedItems.filter(video => video.category === category.id);
              if (categoryVideos.length === 0) return null;

              return (
                <div key={category.id} className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-xl">{category.icon}</span>
                      <h2 className="text-xl font-bold">{category.name}</h2>
                    </div>
                    <Button
                      variant="ghost"
                      className="text-primary-purple hover:text-primary-dark"
                      onClick={() => setActiveCategory(category.id)}
                    >
                      查看更多 <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {categoryVideos.slice(0, 4).map((video) => (
                      <VideoCard
                        key={video.id}
                        video={video}
                        onClick={() => handleVideoClick(video.id)}
                        formatViews={formatViews}
                      />
                    ))}
                  </div>
                </div>
              );
            })
          ) : (
            // 显示选中分类的所有视频
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredVideos.map((video) => (
                <VideoCard
                  key={video.id}
                  video={video}
                  onClick={() => handleVideoClick(video.id)}
                  formatViews={formatViews}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </PublishingLayout>
  );
};

// 视频卡片组件
interface VideoCardProps {
  video: EnhancedVideoMaterial;
  onClick: () => void;
  formatViews: (views: number) => string;
}

const VideoCard = ({ video, onClick, formatViews }: VideoCardProps) => (
  <Card
    className="overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-300 bg-dark-foreground border-gray-700 hover:border-primary-purple/50 hover:-translate-y-1"
    onClick={onClick}
  >
    <div className="aspect-video relative overflow-hidden group">
      <img
        src={video.thumbnails[0] || "/placeholder.svg"}
        alt={video.name}
        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
        <div className="p-4 w-full flex justify-between items-center">
          <span className="text-xs bg-primary-purple px-2 py-1 rounded-full">
            {video.type}
          </span>
          <span className="text-xs bg-black/60 px-2 py-1 rounded-full">
            {video.duration}
          </span>
        </div>
      </div>
      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <div className="bg-primary-purple/80 rounded-full p-3 transform scale-0 group-hover:scale-100 transition-transform duration-300">
          <Play className="h-6 w-6 text-white" />
        </div>
      </div>
    </div>
    <CardContent className="p-4">
      <h3 className="font-semibold mb-2 line-clamp-1">{video.name}</h3>
      <p className="text-sm text-gray-400 line-clamp-2 mb-2">{video.description}</p>
      <div className="flex items-center text-xs text-gray-500">
        <Clock className="h-3 w-3 mr-1" />
        <span>{video.duration}</span>
        <span className="mx-2">•</span>
        <span>{formatViews(video.views)}</span>
      </div>
    </CardContent>
  </Card>
);

export default PublishingHomePage;
