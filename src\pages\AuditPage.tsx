
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AuditStatus, CatalogStatus, MediaType, ProcessingStatus, VideoMaterial, VideoSource } from "@/types";
import { Check, X, Eye } from "lucide-react";
import { useNavigate } from "react-router-dom";

// Mock data for demonstration
const mockAuditItems: VideoMaterial[] = [
  {
    id: "audit-1",
    name: "公司年会宣传视频",
    source: VideoSource.UPLOAD,
    type: MediaType.VIDEO,
    catalogStatus: CatalogStatus.COMPLETED,
    processingStatus: ProcessingStatus.COMPLETED,
    auditStatus: AuditStatus.PENDING,
    publishStatus: undefined,
    createdAt: "2023-05-12",
    submittedAt: "2023-05-13",
    submittedBy: "张三",
    thumbnails: ["/placeholder.svg"],
    duration: 180,
    fileSize: "120MB",
  },
  {
    id: "audit-2",
    name: "产品演示视频",
    source: VideoSource.COLLECTION,
    type: MediaType.VIDEO,
    catalogStatus: CatalogStatus.COMPLETED,
    processingStatus: ProcessingStatus.COMPLETED,
    auditStatus: AuditStatus.PENDING,
    publishStatus: undefined,
    createdAt: "2023-05-10",
    submittedAt: "2023-05-11",
    submittedBy: "李四",
    thumbnails: ["/placeholder.svg"],
    duration: 240,
    fileSize: "150MB",
  },
];

const AuditPage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [sourceFilter, setSourceFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [auditStatusFilter, setAuditStatusFilter] = useState<string>("all");
  const navigate = useNavigate();

  // Filter logic
  const filteredItems = mockAuditItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         (item.submittedBy && item.submittedBy.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesSource = sourceFilter === "all" || item.source === sourceFilter;
    const matchesType = typeFilter === "all" || item.type === typeFilter;
    const matchesStatus = auditStatusFilter === "all" || item.auditStatus === auditStatusFilter;

    return matchesSearch && matchesSource && matchesType && matchesStatus;
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">审核库</h1>
        <Card>
          <CardHeader>
            <CardTitle>待审核内容</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <Input
                    placeholder="搜索名称、提交人..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="w-[150px]">
                  <Select value={sourceFilter} onValueChange={setSourceFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="来源筛选" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部来源</SelectItem>
                      <SelectItem value={VideoSource.UPLOAD}>上传</SelectItem>
                      <SelectItem value={VideoSource.COLLECTION}>收录</SelectItem>
                      <SelectItem value={VideoSource.IMPORT}>导入</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-[150px]">
                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="类型筛选" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value={MediaType.VIDEO}>视频</SelectItem>
                      <SelectItem value={MediaType.AUDIO}>音频</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-[150px]">
                  <Select value={auditStatusFilter} onValueChange={setAuditStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="审核状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value={AuditStatus.PENDING}>待审核</SelectItem>
                      <SelectItem value={AuditStatus.APPROVED}>已通过</SelectItem>
                      <SelectItem value={AuditStatus.REJECTED}>未通过</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>名称</TableHead>
                    <TableHead>来源</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>提交人</TableHead>
                    <TableHead>审核状态</TableHead>
                    <TableHead>提交时间</TableHead>
                    <TableHead>审核时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredItems.length > 0 ? (
                    filteredItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.source}</TableCell>
                        <TableCell>{item.type}</TableCell>
                        <TableCell>{item.submittedBy || "-"}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            item.auditStatus === AuditStatus.APPROVED ? 'bg-green-200 text-green-800' : 
                            item.auditStatus === AuditStatus.REJECTED ? 'bg-red-200 text-red-800' : 
                            'bg-yellow-200 text-yellow-800'
                          }`}>
                            {item.auditStatus}
                          </span>
                        </TableCell>
                        <TableCell>{item.submittedAt || "-"}</TableCell>
                        <TableCell>{item.auditedAt || "-"}</TableCell>
                        <TableCell className="text-right">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="mr-2"
                            onClick={() => navigate(`/audits/${item.id}`)}
                          >
                            <Eye className="h-4 w-4 mr-1" /> 查看
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-4">
                        暂无符合条件的审核内容
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default AuditPage;
