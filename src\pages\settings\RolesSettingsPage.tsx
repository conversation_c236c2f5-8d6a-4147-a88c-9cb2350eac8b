
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Plus<PERSON>ircle, Pencil, Trash, Check } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/hooks/use-toast";

// Mock data for roles
interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
}

const mockRoles: Role[] = [
  {
    id: "role-1",
    name: "超级管理员",
    description: "拥有系统所有权限",
    permissions: ["all"],
    userCount: 2,
  },
  {
    id: "role-2",
    name: "编目员",
    description: "负责内容编目操作",
    permissions: ["materials:read", "materials:catalog"],
    userCount: 8,
  },
  {
    id: "role-3",
    name: "审核员",
    description: "负责内容审核操作",
    permissions: ["materials:read", "audits:read", "audits:approve"],
    userCount: 5,
  },
  {
    id: "role-4",
    name: "发布员",
    description: "负责内容发布操作",
    permissions: ["materials:read", "published:read", "published:manage"],
    userCount: 3,
  },
];

const RolesSettingsPage = () => {
  const [roles, setRoles] = useState<Role[]>(mockRoles);

  // Mock handlers for various actions
  const handleCreateRole = () => {
    toast({
      title: "创建角色",
      description: "此处将打开角色创建表单",
    });
  };

  const handleEditPermissions = (roleId: string) => {
    toast({
      title: "编辑权限",
      description: `此处将打开ID为${roleId}的角色权限编辑界面`,
    });
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">角色管理</h1>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>角色管理</CardTitle>
              <CardDescription>管理系统角色及其权限</CardDescription>
            </div>
            <Button onClick={handleCreateRole} className="flex items-center">
              <PlusCircle className="mr-2 h-4 w-4" />
              新建角色
            </Button>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>角色名称</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead>用户数量</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {roles.map((role) => (
                  <TableRow key={role.id}>
                    <TableCell className="font-medium">{role.name}</TableCell>
                    <TableCell>{role.description}</TableCell>
                    <TableCell>{role.userCount}</TableCell>
                    <TableCell className="text-right">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mr-1"
                        onClick={() => handleEditPermissions(role.id)}
                      >
                        <Check className="h-4 w-4 mr-1" />
                        权限管理
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="mr-1"
                        onClick={() => toast({ title: "编辑角色", description: `此处将打开ID为${role.id}的角色编辑表单` })}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        disabled={role.name === "超级管理员" || role.userCount > 0}
                        onClick={() => toast({ 
                          title: "删除角色", 
                          description: `此处将确认删除ID为${role.id}的角色`, 
                          variant: "destructive" 
                        })}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default RolesSettingsPage;
