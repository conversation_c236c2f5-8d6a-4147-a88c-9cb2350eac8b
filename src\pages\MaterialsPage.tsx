import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { api } from "@/services/api";
import { VideoMaterial, SearchFilters, VideoSource, MediaType, ProcessingStatus, CatalogStatus } from "@/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Pencil, Trash2, Upload, CheckCircle } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { AppLayout } from "@/components/layout/AppLayout";

const MaterialsPage = () => {
  const [materials, setMaterials] = useState<VideoMaterial[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    loadMaterials();
  }, [filters]);

  const loadMaterials = async () => {
    setIsLoading(true);
    try {
      const data = await api.getMaterialList(filters);
      setMaterials(data);
    } catch (error) {
      console.error("Failed to load materials", error);
      toast({
        title: "错误",
        description: "加载素材列表失败",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
    }));
  };

  const resetFilters = () => {
    setFilters({});
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast({
        title: "错误",
        description: "请选择要上传的文件",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    try {
      const newMaterial = await api.uploadMaterial(selectedFile);
      toast({
        title: "上传成功",
        description: `素材 ${newMaterial.name} 已上传并等待处理`,
      });
      setUploadDialogOpen(false);
      setSelectedFile(null);
      loadMaterials();
    } catch (error) {
      console.error("Upload failed", error);
      toast({
        title: "错误",
        description: "上传素材失败",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await api.deleteMaterial(id);
      toast({
        title: "成功",
        description: "素材已移至回收站",
      });
      loadMaterials();
    } catch (error) {
      console.error("Delete failed", error);
      toast({
        title: "错误",
        description: "删除素材失败",
        variant: "destructive",
      });
    }
  };

  const handleSubmitForAudit = async (id: string) => {
    try {
      await api.submitForAudit(id);
      toast({
        title: "成功",
        description: "素材已提交审核",
      });
      loadMaterials();
    } catch (error) {
      console.error("Submit for audit failed", error);
      toast({
        title: "错误",
        description: "提交审核失败",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">素材库</h1>
          <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-primary-purple hover:bg-primary-dark">
                <Upload className="mr-2 h-4 w-4" />
                上传素材
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>上传视频素材</DialogTitle>
                <DialogDescription>
                  请选择要上传的视频或音频文件
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="file">选择文件</Label>
                  <Input
                    id="file"
                    type="file"
                    accept="video/*,audio/*"
                    onChange={handleFileChange}
                  />
                </div>
                {selectedFile && (
                  <div className="text-sm text-gray-400">
                    已选择: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)}MB)
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setUploadDialogOpen(false)}>
                  取消
                </Button>
                <Button 
                  className="bg-primary-purple hover:bg-primary-dark" 
                  onClick={handleUpload} 
                  disabled={isUploading || !selectedFile}
                >
                  {isUploading ? "上传中..." : "上传"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Card className="glass-morphism">
          <CardHeader>
            <CardTitle>筛选条件</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">素材名称</Label>
                <Input
                  id="name"
                  placeholder="搜索名称"
                  value={filters.name || ""}
                  onChange={(e) => handleFilterChange("name", e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="source">来源</Label>
                <Select
                  value={filters.source || ""}
                  onValueChange={(value) => handleFilterChange("source", value as VideoSource)}
                >
                  <SelectTrigger id="source">
                    <SelectValue placeholder="全部来源" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部来源</SelectItem>
                    {Object.values(VideoSource).map((source) => (
                      <SelectItem key={source} value={source}>{source}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="type">类型</Label>
                <Select
                  value={filters.type || ""}
                  onValueChange={(value) => handleFilterChange("type", value as MediaType)}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="全部类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    {Object.values(MediaType).map((type) => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="processingStatus">处理状态</Label>
                <Select
                  value={filters.processingStatus || ""}
                  onValueChange={(value) => handleFilterChange("processingStatus", value as ProcessingStatus)}
                >
                  <SelectTrigger id="processingStatus">
                    <SelectValue placeholder="全部状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    {Object.values(ProcessingStatus).map((status) => (
                      <SelectItem key={status} value={status}>{status}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="catalogStatus">编目状态</Label>
                <Select
                  value={filters.catalogStatus || ""}
                  onValueChange={(value) => handleFilterChange("catalogStatus", value as CatalogStatus)}
                >
                  <SelectTrigger id="catalogStatus">
                    <SelectValue placeholder="全部状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    {Object.values(CatalogStatus).map((status) => (
                      <SelectItem key={status} value={status}>{status}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="startDate">开始日期</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={filters.startDate || ""}
                  onChange={(e) => handleFilterChange("startDate", e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="endDate">结束日期</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={filters.endDate || ""}
                  onChange={(e) => handleFilterChange("endDate", e.target.value)}
                />
              </div>
              
              <div className="flex items-end space-x-2">
                <Button variant="outline" onClick={resetFilters}>重置</Button>
                <Button className="bg-primary-purple hover:bg-primary-dark" onClick={loadMaterials}>筛选</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-morphism overflow-hidden">
          <CardHeader>
            <CardTitle>素材列表</CardTitle>
          </CardHeader>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>素材名称</TableHead>
                  <TableHead>来源</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>编目状态</TableHead>
                  <TableHead>处理状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-10">
                      加载中...
                    </TableCell>
                  </TableRow>
                ) : materials.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-10">
                      没有找到符合条件的素材
                    </TableCell>
                  </TableRow>
                ) : (
                  materials.map((material) => (
                    <TableRow key={material.id}>
                      <TableCell className="font-medium">{material.name}</TableCell>
                      <TableCell>{material.source}</TableCell>
                      <TableCell>{material.type}</TableCell>
                      <TableCell>{material.catalogStatus}</TableCell>
                      <TableCell>{material.processingStatus}</TableCell>
                      <TableCell>{formatDate(material.createdAt)}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="icon" 
                            onClick={() => navigate(`/materials/${material.id}`)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleSubmitForAudit(material.id)}
                            disabled={material.processingStatus !== ProcessingStatus.COMPLETED}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                          
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => handleDelete(material.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </Card>
      </div>
    </AppLayout>
  );
};

export default MaterialsPage;
