import { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { api } from "@/services/api";
import { VideoMaterial, ChatMessage } from "@/types";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

import { toast } from "@/components/ui/use-toast";
import { AppLayout } from "@/components/layout/AppLayout";
import { FileText, ArrowLeft, Send, Clock, CheckCircle } from "lucide-react";
import { Label } from "@/components/ui/label";

// Video Player Component
const VideoPlayer = ({ src }: { src: string }) => {
  const videoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateVideoHeight = () => {
      if (videoRef.current) {
        const height = videoRef.current.offsetHeight;
        document.documentElement.style.setProperty('--video-height', `${height}px`);
      }
    };

    // 初始设置
    updateVideoHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', updateVideoHeight);

    // 清理函数
    return () => {
      window.removeEventListener('resize', updateVideoHeight);
    };
  }, []);

  return (
    <div ref={videoRef} className="relative aspect-video w-full overflow-hidden rounded-md">
      <video
        controls
        className="h-full w-full"
        poster="https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGNvZGV8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60"
      >
        <source src={src} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </div>
  );
};



// Transcript Component
const Transcript = ({
  transcript
}: {
  transcript: { startTime: number, endTime: number, text: string }[] | { time: string, text: string }[]
}) => (
  <div className="space-y-2">
    {transcript.map((item, index) => (
      <div key={index} className="flex text-sm hover:bg-gray-800 p-2 rounded-md transition-colors">
        <span className="w-20 flex-shrink-0 text-gray-400">
          {'startTime' in item
            ? new Date(item.startTime * 1000).toISOString().substring(14, 5)
            : 'time' in item ? item.time : ''
          }
        </span>
        <span>{item.text}</span>
      </div>
    ))}
  </div>
);

// TranscriptPanel Component - matches VideoPlayer structure exactly
const TranscriptPanel = ({
  transcript
}: {
  transcript?: { startTime: number, endTime: number, text: string }[] | { time: string, text: string }[]
}) => (
  <div className="relative w-full h-[var(--video-height,0px)] overflow-hidden rounded-md bg-gray-900">
    {transcript ? (
      <div className="absolute inset-0 overflow-y-auto p-4 scrollbar-thin">
        <Transcript transcript={transcript} />
      </div>
    ) : (
      <div className="absolute inset-0 flex items-center justify-center text-gray-400">
        暂无配音文稿
      </div>
    )}
  </div>
);

// Feature List Component
const FeatureList = ({ features }: { features: VideoMaterial["features"] }) => (
  <div className="space-y-3">
    {features?.map((feature) => (
      <div key={feature.id || Math.random().toString()} className="flex items-start p-2 hover:bg-gray-800 rounded-md transition-colors">
        {feature.imageUrl ? (
          <img
            src={feature.imageUrl}
            alt={feature.name}
            className="h-12 w-12 object-cover rounded-md mr-3"
          />
        ) : (
          <div className="h-12 w-12 bg-gray-700 rounded-md mr-3 flex items-center justify-center">
            <span className="text-xs text-gray-400">No Image</span>
          </div>
        )}
        <div className="flex-1">
          <div className="flex justify-between items-start">
            <div>
              <Badge variant={feature.type === "敏感人脸" ? "destructive" : feature.type === "敏感标志物" ? "outline" : "secondary"}>
                {feature.type}
              </Badge>
              <h4 className="font-medium mt-1">{feature.name}</h4>
            </div>
            <div className="text-xs text-gray-400 flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              <span>
                {'timestamp' in feature
                  ? new Date(feature.timestamp * 1000).toISOString().substring(14, 5)
                  : 'time' in feature ? feature.time : ''
                }
              </span>
            </div>
          </div>
          {'confidence' in feature && typeof feature.confidence === 'number' && (
            <div className="mt-1 text-sm text-gray-400">
              置信度: {Math.round(feature.confidence * 100)}%
            </div>
          )}
        </div>
      </div>
    ))}
  </div>
);

// FeaturesPanel Component - matches VideoPlayer structure exactly
const FeaturesPanel = ({
  features
}: {
  features?: VideoMaterial["features"]
}) => (
  <div className="relative w-full h-[var(--video-height,0px)] overflow-hidden rounded-md bg-gray-900">
    {features && features.length > 0 ? (
      <div className="absolute inset-0 overflow-y-auto p-4 scrollbar-thin">
        <FeatureList features={features} />
      </div>
    ) : (
      <div className="absolute inset-0 flex items-center justify-center text-gray-400">
        暂无特征信息
      </div>
    )}
  </div>
);

// AI Chat Component
const AIChat = ({ videoId }: { videoId: string }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 'system-1',
      role: 'assistant',
      content: '您好，我是视频智能助手。您可以询问我关于这个视频的任何问题，我会尽力回答。',
      timestamp: Date.now()
    }
  ]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const sendMessage = async () => {
    if (input.trim() === "") return;

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: input,
      timestamp: Date.now()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    try {
      const response = await api.chatWithAI(videoId, input);
      setMessages(prev => [...prev, response]);
    } catch (error) {
      console.error("Failed to get AI response", error);
      toast({
        title: "错误",
        description: "获取AI回复失败",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto space-y-4 mb-4">
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`flex ${
              msg.role === "user" ? "justify-end" : "justify-start"
            }`}
          >
            <div
              className={`max-w-[75%] rounded-lg p-3 ${
                msg.role === "user"
                  ? "bg-primary-purple text-white"
                  : "bg-gray-800 text-white"
              }`}
            >
              {msg.content}
            </div>
          </div>
        ))}
        {isLoading && (
          <div className="flex justify-start">
            <div className="max-w-[75%] rounded-lg p-3 bg-gray-800 text-white">
              <div className="flex space-x-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-100"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-200"></div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="输入您的问题..."
          onKeyDown={(e) => e.key === "Enter" && sendMessage()}
        />
        <Button
          onClick={sendMessage}
          disabled={isLoading || !input.trim()}
          className="bg-primary-purple hover:bg-primary-dark"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

// AIChatPanel Component - matches VideoPlayer structure exactly
const AIChatPanel = ({ videoId }: { videoId: string }) => (
  <div className="relative w-full h-[var(--video-height,0px)] overflow-hidden rounded-md bg-gray-900">
    <div className="absolute inset-0 p-4">
      <AIChat videoId={videoId} />
    </div>
  </div>
);

// CatalogEditor Component
const CatalogEditor = ({
  material,
  setMaterial
}: {
  material: VideoMaterial,
  setMaterial: React.Dispatch<React.SetStateAction<VideoMaterial | null>>
}) => {
  // 初始化编辑状态
  const [catalogInfo, setCatalogInfo] = useState<Record<string, string>>(
    material.catalogInfo || {
      "标题": "",
      "类别": "",
      "关键词": "",
      "简介": "",
      "地点": "",
      "时间": ""
    }
  );

  const [isEditing, setIsEditing] = useState(!material.catalogInfo);
  const [isSaving, setIsSaving] = useState(false);

  // 处理字段变更
  const handleFieldChange = (key: string, value: string) => {
    setCatalogInfo(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 添加新字段
  const [newFieldKey, setNewFieldKey] = useState("");
  const [newFieldValue, setNewFieldValue] = useState("");

  const handleAddField = () => {
    if (newFieldKey.trim() === "") return;

    setCatalogInfo(prev => ({
      ...prev,
      [newFieldKey]: newFieldValue
    }));

    setNewFieldKey("");
    setNewFieldValue("");
  };

  // 删除字段
  const handleRemoveField = (key: string) => {
    setCatalogInfo(prev => {
      const newInfo = { ...prev };
      delete newInfo[key];
      return newInfo;
    });
  };

  // 保存编目信息
  const handleSaveCatalog = async () => {
    setIsSaving(true);
    try {
      const updatedMaterial = await api.updateMaterial(material.id, {
        catalogInfo
      });
      setMaterial(updatedMaterial);
      setIsEditing(false);
      toast({
        title: "成功",
        description: "编目信息已保存",
      });
    } catch (error) {
      console.error("Failed to save catalog info", error);
      toast({
        title: "错误",
        description: "保存编目信息失败",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (!isEditing) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(catalogInfo).map(([key, value]) => (
            <div key={key} className="space-y-1">
              <Label className="text-gray-400">{key}</Label>
              <div className="bg-gray-800 p-2 rounded-md">{value}</div>
            </div>
          ))}
        </div>
        <div className="flex justify-end">
          <Button
            className="bg-primary-purple hover:bg-primary-dark"
            onClick={() => setIsEditing(true)}
          >
            编辑编目
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(catalogInfo).map(([key, value]) => (
          <div key={key} className="space-y-1">
            <div className="flex justify-between items-center">
              <Label className="text-gray-400">{key}</Label>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-gray-400 hover:text-red-500"
                onClick={() => handleRemoveField(key)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </Button>
            </div>
            <Input
              value={value}
              onChange={(e) => handleFieldChange(key, e.target.value)}
              className="bg-gray-800 border-gray-700"
            />
          </div>
        ))}
      </div>

      <div className="space-y-2">
        <Label className="text-gray-400">添加新字段</Label>
        <div className="flex space-x-2">
          <Input
            placeholder="字段名称"
            value={newFieldKey}
            onChange={(e) => setNewFieldKey(e.target.value)}
            className="bg-gray-800 border-gray-700"
          />
          <Input
            placeholder="字段值"
            value={newFieldValue}
            onChange={(e) => setNewFieldValue(e.target.value)}
            className="bg-gray-800 border-gray-700"
          />
          <Button
            variant="outline"
            onClick={handleAddField}
          >
            添加
          </Button>
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button
          variant="outline"
          onClick={() => {
            setCatalogInfo(material.catalogInfo || {});
            setIsEditing(false);
          }}
        >
          取消
        </Button>
        <Button
          className="bg-primary-purple hover:bg-primary-dark"
          onClick={handleSaveCatalog}
          disabled={isSaving}
        >
          {isSaving ? "保存中..." : "保存编目"}
        </Button>
      </div>
    </div>
  );
};

const MaterialDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [material, setMaterial] = useState<VideoMaterial | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [summary, setSummary] = useState("");
  const [isGeneratingDocument, setIsGeneratingDocument] = useState(false);

  // State for photo wall lightbox
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Handle keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedImage || !material?.thumbnails) return;

      // Close lightbox on Escape key
      if (e.key === 'Escape') {
        setSelectedImage(null);
        return;
      }

      const currentIndex = material.thumbnails.indexOf(selectedImage);

      // Navigate to previous image on left arrow
      if (e.key === 'ArrowLeft' && currentIndex > 0) {
        setSelectedImage(material.thumbnails[currentIndex - 1]);
      }

      // Navigate to next image on right arrow
      if (e.key === 'ArrowRight' && currentIndex < material.thumbnails.length - 1) {
        setSelectedImage(material.thumbnails[currentIndex + 1]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedImage, material]);

  useEffect(() => {
    if (!id) return;

    const loadMaterial = async () => {
      setIsLoading(true);
      try {
        const data = await api.getMaterialById(id);
        if (data) {
          setMaterial(data);
          setSummary(data.summary || "");
        }
      } catch (error) {
        console.error("Failed to load material details", error);
        toast({
          title: "错误",
          description: "加载素材详情失败",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadMaterial();
  }, [id]);

  const handleSaveSummary = async () => {
    if (!material) return;

    try {
      const updatedMaterial = await api.updateMaterial(material.id, { summary });
      setMaterial(updatedMaterial);
      toast({
        title: "成功",
        description: "内容摘要已保存",
      });
    } catch (error) {
      console.error("Failed to save summary", error);
      toast({
        title: "错误",
        description: "保存内容摘要失败",
        variant: "destructive",
      });
    }
  };

  const handleGenerateDocument = async () => {
    if (!material) return;

    setIsGeneratingDocument(true);
    try {
      const result = await api.generateDocument(material.id);
      toast({
        title: "成功",
        description: result,
      });
    } catch (error) {
      console.error("Failed to generate document", error);
      toast({
        title: "错误",
        description: "生成文书失败",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingDocument(false);
    }
  };



  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-full">
          <div className="text-xl">加载中...</div>
        </div>
      </AppLayout>
    );
  }

  if (!material) {
    return (
      <AppLayout>
        <div className="flex flex-col items-center justify-center h-full">
          <div className="text-xl mb-4">素材不存在</div>
          <Button onClick={() => navigate("/materials")}>返回素材库</Button>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      {/* Lightbox for full-size image viewing */}
      {selectedImage && material?.thumbnails && (
        <div
          className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-[90vh] w-full">
            <img
              src={selectedImage}
              alt="Full size"
              className="w-full h-full object-contain"
            />

            {/* Close button */}
            <Button
              variant="outline"
              size="icon"
              className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white border-none"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedImage(null);
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </Button>

            {/* Navigation buttons */}
            <div className="absolute inset-y-0 left-0 right-0 flex justify-between items-center px-4">
              {/* Previous button */}
              {material.thumbnails.indexOf(selectedImage) > 0 && (
                <Button
                  variant="outline"
                  size="icon"
                  className="bg-black/50 hover:bg-black/70 text-white border-none h-10 w-10"
                  onClick={(e) => {
                    e.stopPropagation();
                    const currentIndex = material.thumbnails!.indexOf(selectedImage);
                    if (currentIndex > 0) {
                      setSelectedImage(material.thumbnails![currentIndex - 1]);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                    <polyline points="15 18 9 12 15 6"></polyline>
                  </svg>
                </Button>
              )}

              {/* Next button */}
              {material.thumbnails.indexOf(selectedImage) < material.thumbnails.length - 1 && (
                <Button
                  variant="outline"
                  size="icon"
                  className="bg-black/50 hover:bg-black/70 text-white border-none h-10 w-10"
                  onClick={(e) => {
                    e.stopPropagation();
                    const currentIndex = material.thumbnails!.indexOf(selectedImage);
                    if (currentIndex < material.thumbnails!.length - 1) {
                      setSelectedImage(material.thumbnails![currentIndex + 1]);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </Button>
              )}
            </div>

            {/* Image counter */}
            <div className="absolute bottom-2 left-0 right-0 text-center text-white text-sm">
              {material.thumbnails.indexOf(selectedImage) + 1} / {material.thumbnails.length}
            </div>
          </div>
        </div>
      )}

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" onClick={() => navigate("/materials")}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">{material.name}</h1>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              className="bg-primary-purple hover:bg-primary-dark"
              onClick={handleGenerateDocument}
              disabled={isGeneratingDocument}
            >
              <FileText className="h-4 w-4 mr-2" />
              {isGeneratingDocument ? "生成中..." : "生成文书"}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Video Player and Thumbnails */}
          <div className="lg:col-span-2 space-y-4">
            <Card className="glass-morphism overflow-hidden">
              <CardContent className="p-0">
                <Tabs defaultValue="preview">
                  <TabsList className="w-full grid grid-cols-2">
                    <TabsTrigger value="preview">视频预览</TabsTrigger>
                    <TabsTrigger value="photowall">照片墙</TabsTrigger>
                  </TabsList>
                  <div className="p-4">
                    <TabsContent value="preview" className="mt-0">
                      <VideoPlayer src="https://sample-videos.com/video123/mp4/720/big_buck_bunny_720p_1mb.mp4" />
                    </TabsContent>
                    <TabsContent value="photowall" className="mt-0">
                      {material.thumbnails && (
                        <div className="relative aspect-video w-full overflow-hidden rounded-md bg-gray-900 flex items-center justify-center">
                          <div className="w-full h-full overflow-y-auto p-4">
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                              {material.thumbnails.map((thumbnail, index) => (
                                <div
                                  key={index}
                                  className="relative group cursor-pointer"
                                  onClick={() => setSelectedImage(thumbnail)}
                                >
                                  <img
                                    src={thumbnail}
                                    alt={`缩略图 ${index + 1}`}
                                    className="w-full aspect-video object-cover rounded-md transition-transform duration-300 group-hover:scale-105"
                                  />
                                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center rounded-md">
                                    <span className="text-white text-sm font-medium">点击查看</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>

            <Card className="glass-morphism">
              <CardContent className="p-0">
                <Tabs defaultValue="summary">
                  <TabsList className="w-full grid grid-cols-2">
                    <TabsTrigger value="summary">内容摘要</TabsTrigger>
                    <TabsTrigger value="info">视频基本信息</TabsTrigger>
                  </TabsList>
                  <div className="p-4">
                    <TabsContent value="summary" className="mt-0">
                      <div className="space-y-4">
                        <Textarea
                          value={summary}
                          onChange={(e) => setSummary(e.target.value)}
                          placeholder="请输入视频内容摘要..."
                          className="min-h-[100px]"
                        />
                        <div className="flex justify-end">
                          <Button
                            className="bg-primary-purple hover:bg-primary-dark"
                            onClick={handleSaveSummary}
                          >
                            保存摘要
                          </Button>
                        </div>
                      </div>
                    </TabsContent>
                    <TabsContent value="info" className="mt-0">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {material.duration && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">时长</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{typeof material.duration === 'number' ? `${Math.floor(material.duration / 60)}:${(material.duration % 60).toString().padStart(2, '0')}` : material.duration}</div>
                          </div>
                        )}
                        {material.resolution && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">分辨率</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.resolution}</div>
                          </div>
                        )}
                        {material.bitrate && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">码率</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.bitrate}</div>
                          </div>
                        )}
                        {material.codec && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">编码格式</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.codec}</div>
                          </div>
                        )}
                        {material.format && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">文件格式</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.format}</div>
                          </div>
                        )}
                        {material.fileSize && (
                          <div className="space-y-1">
                            <Label className="text-gray-400">文件大小</Label>
                            <div className="bg-gray-800 p-2 rounded-md">{material.fileSize}</div>
                          </div>
                        )}
                      </div>
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>

            <Card className="glass-morphism">
              <CardContent className="p-0">
                <Tabs defaultValue="catalog">
                  <TabsList className="w-full grid grid-cols-1">
                    <TabsTrigger value="catalog">人工编目</TabsTrigger>
                  </TabsList>
                  <div className="p-4">
                    <TabsContent value="catalog" className="mt-0">
                      <CatalogEditor material={material} setMaterial={setMaterial} />
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Video Information and Tabs */}
          <div className="space-y-6">
            <Card className="glass-morphism overflow-hidden">
              <CardContent className="p-0">
                <Tabs defaultValue="transcript">
                  <TabsList className="w-full grid grid-cols-3">
                    <TabsTrigger value="transcript">配音文稿</TabsTrigger>
                    <TabsTrigger value="features">特征信息</TabsTrigger>
                    <TabsTrigger value="ai">AI问答</TabsTrigger>
                  </TabsList>
                  <div className="p-4">
                    <TabsContent value="transcript" className="mt-0">
                      <TranscriptPanel transcript={material.transcript} />
                    </TabsContent>
                    <TabsContent value="features" className="mt-0">
                      <FeaturesPanel features={material.features} />
                    </TabsContent>
                    <TabsContent value="ai" className="mt-0">
                      <AIChatPanel videoId={material.id} />
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>


      </div>
    </AppLayout>
  );
};

export default MaterialDetailPage;
