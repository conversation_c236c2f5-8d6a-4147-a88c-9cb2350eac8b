import { createRoot } from 'react-dom/client'
import { PublishingApp } from './publishing/PublishingApp'
import './index.css'

// 设置全局变量，标识这是内容发布系统
if (typeof window !== 'undefined') {
  (window as any).IS_PUBLISHING_SYSTEM = true;
  console.log('publishing.tsx: Setting window.IS_PUBLISHING_SYSTEM =', (window as any).IS_PUBLISHING_SYSTEM);
}

// Entry point for the publishing subsystem
console.log('Loading publishing system from src/publishing.tsx');
createRoot(document.getElementById("root")!).render(<PublishingApp />);
