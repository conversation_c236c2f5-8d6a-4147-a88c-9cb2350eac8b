
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Search, FileDown } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/hooks/use-toast";

interface LogEntry {
  id: string;
  timestamp: string;
  username: string;
  operation: string;
  type: 'login' | 'catalog' | 'audit' | 'system';
  details: string;
  ip?: string;
}

// Mock data for logs
const mockLogs: LogEntry[] = [
  {
    id: "log-1",
    timestamp: "2023-05-15 09:15:42",
    username: "admin",
    operation: "登录系统",
    type: "login",
    details: "管理员登录成功",
    ip: "*************",
  },
  {
    id: "log-2",
    timestamp: "2023-05-15 10:23:15",
    username: "张三",
    operation: "素材编目",
    type: "catalog",
    details: "编辑素材 #12345 的编目信息",
  },
  {
    id: "log-3",
    timestamp: "2023-05-15 11:05:30",
    username: "李四",
    operation: "素材审核",
    type: "audit",
    details: "审核通过素材 #12345，标题：'公司年会宣传视频'",
  },
  {
    id: "log-4",
    timestamp: "2023-05-15 13:42:10",
    username: "system",
    operation: "系统维护",
    type: "system",
    details: "系统定时数据备份完成",
  },
  {
    id: "log-5",
    timestamp: "2023-05-15 15:18:22",
    username: "王五",
    operation: "素材编目",
    type: "catalog",
    details: "编辑素材 #12347 的编目信息",
  },
  {
    id: "log-6",
    timestamp: "2023-05-15 16:35:47",
    username: "admin",
    operation: "角色管理",
    type: "system",
    details: "创建新角色 '普通编辑'",
    ip: "*************",
  },
  {
    id: "log-7",
    timestamp: "2023-05-15 17:22:05",
    username: "赵六",
    operation: "素材审核",
    type: "audit",
    details: "审核拒绝素材 #12348，标题：'测试视频'，原因：'内容不完整'",
  },
];

const LogsPage = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [operationType, setOperationType] = useState<string>("all");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  
  // Filter logic
  const filteredLogs = mockLogs.filter(log => {
    const matchesSearch = 
      log.username.toLowerCase().includes(searchQuery.toLowerCase()) || 
      log.operation.toLowerCase().includes(searchQuery.toLowerCase()) || 
      log.details.toLowerCase().includes(searchQuery.toLowerCase());
      
    const matchesType = operationType === "all" || log.type === operationType;
    
    const matchesDateRange = (!startDate || log.timestamp >= startDate) && 
                             (!endDate || log.timestamp <= endDate + " 23:59:59");
    
    return matchesSearch && matchesType && matchesDateRange;
  });

  const handleExportLogs = () => {
    toast({
      title: "导出成功",
      description: "日志已成功导出为CSV文件",
    });
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">日志管理</h1>
        <Card>
          <CardHeader>
            <CardTitle>操作日志</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索用户名、操作、详情..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
                <div className="w-[150px]">
                  <Select value={operationType} onValueChange={setOperationType}>
                    <SelectTrigger>
                      <SelectValue placeholder="操作类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="login">登录</SelectItem>
                      <SelectItem value="catalog">编目</SelectItem>
                      <SelectItem value="audit">审核</SelectItem>
                      <SelectItem value="system">系统</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="w-[160px]">
                  <Input
                    type="date"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                  />
                </div>
                <span className="self-center">至</span>
                <div className="w-[160px]">
                  <Input
                    type="date"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
                <Button variant="outline" className="flex items-center gap-2" onClick={handleExportLogs}>
                  <FileDown className="h-4 w-4" />
                  导出日志
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>时间</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>操作</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead className="w-[300px]">详情</TableHead>
                    <TableHead>IP地址</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.length > 0 ? (
                    filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>{log.timestamp}</TableCell>
                        <TableCell>{log.username}</TableCell>
                        <TableCell>{log.operation}</TableCell>
                        <TableCell>
                          <Badge 
                            variant="outline"
                            className={`
                              ${log.type === 'login' ? 'bg-blue-100 text-blue-800 border-blue-300' : ''}
                              ${log.type === 'catalog' ? 'bg-green-100 text-green-800 border-green-300' : ''}
                              ${log.type === 'audit' ? 'bg-purple-100 text-purple-800 border-purple-300' : ''}
                              ${log.type === 'system' ? 'bg-amber-100 text-amber-800 border-amber-300' : ''}
                            `}
                          >
                            {log.type === 'login' && '登录'}
                            {log.type === 'catalog' && '编目'}
                            {log.type === 'audit' && '审核'}
                            {log.type === 'system' && '系统'}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-[300px] truncate">{log.details}</TableCell>
                        <TableCell>{log.ip || '-'}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        暂无符合条件的日志记录
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default LogsPage;
